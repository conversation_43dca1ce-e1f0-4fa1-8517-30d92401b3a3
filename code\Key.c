#include "Key.h"

#define Key_Time	20

bit K1_Press = 0;
bit K2_Press = 0;
bit K3_Press = 0;
bit K4_Press = 0;
bit K5_Press = 0;

unsigned char K1_Count = 0;
unsigned char K2_Count = 0;
unsigned char K3_Count = 0;
unsigned char K4_Count = 0;
unsigned char K5_Count = 0;

unsigned char Key_Buff = 0x00;


void Key_Scan(void)
{
    // K1按键处理
    if(K1 == 0)  // 检测K1是否按下
    {
        if(K1_Press == 0)  // 如果之前未按下
        {
            if(K1_Count >= Key_Time)  // 达到消抖时间
            {
                K1_Press = 1;  // 标记K1为按下状态
                K1_Count = 0;   // 计数器清零
            }
            else
            {
                K1_Count++;  // 计数器递增
            }
        }
        else
        {
            K1_Count = 0;  // 已按下状态，计数器清零
        }
    }
    else  // K1未按下
    {
        if(K1_Press == 1)  // 如果之前是按下状态
        {
            if(K1_Count >= Key_Time)  // 达到消抖时间
            {
                K1_Press = 0;  // 标记K1为松开状态
                K1_Count = 0;   // 计数器清零
            }
            else
            {
                K1_Count++;  // 计数器递增
            }
        }
        else
        {
            K1_Count = 0;  // 已松开状态，计数器清零
        }
    }

    // K2按键处理（逻辑与K1相同）
    if(K2 == 0)
    {
        if(K2_Press == 0)
        {
            if(K2_Count >= Key_Time)
            {
                K2_Press = 1;
                K2_Count = 0;
            }
            else
            {
                K2_Count++;
            }
        }
        else
        {
            K2_Count = 0;
        }
    }
    else
    {
        if(K2_Press == 1)
        {
            if(K2_Count >= Key_Time)
            {
                K2_Press = 0;
                K2_Count = 0;
            }
            else
            {
                K2_Count++;
            }
        }
        else
        {
            K2_Count = 0;
        }
    }

    // K3按键处理（逻辑与K1相同）
    if(K3 == 0)
    {
        if(K3_Press == 0)
        {
            if(K3_Count >= Key_Time)
            {
                K3_Press = 1;
                K3_Count = 0;
            }
            else
            {
                K3_Count++;
            }
        }
        else
        {
            K3_Count = 0;
        }
    }
    else
    {
        if(K3_Press == 1)
        {
            if(K3_Count >= Key_Time)
            {
                K3_Press = 0;
                K3_Count = 0;
            }
            else
            {
                K3_Count++;
            }
        }
        else
        {
            K3_Count = 0;
        }
    }

    // 充电状态处理
    if(CHG_CHARGE == 0)  // 检测是否在充电，== 0 表示设备正在充电
    {
        if(K4_Press == 0)  // 如果之前未充电
        {
            if(K4_Count >= Key_Time)  // 达到消抖时间
            {
                K4_Press = 1;  // 标记为充电状态
                K4_Count = 0;   // 计数器清零
            }
            else
            {
                K4_Count++;  // 计数器递增
            }
        }
        else
        {
            K4_Count = 0;  // 已充电状态，计数器清零
        }
    }
    else  // 未在充电
    {
        if(K4_Press == 1)  // 如果之前是充电状态
        {
            if(K4_Count >= Key_Time)  // 达到消抖时间
            {
                K4_Press = 0;  // 标记为未充电状态
                K4_Count = 0;   // 计数器清零
            }
            else
            {
                K4_Count++;  // 计数器递增
            }
        }
        else
        {
            K4_Count = 0;  // 不再充电状态，计数器清零
        }
    }

    // 充满电状态处理
    if(CHG_FULL == 0)  // 检测是否充满电，==0表示未充满电，!= 0 表示已充满电
    {
        if(K5_Press == 0)  // 如果之前未充满
        {
            if(K5_Count >= Key_Time)  // 达到消抖时间
            {
                K5_Press = 1;  // 标记为充满状态
                K5_Count = 0;   // 计数器清零
            }
            else
            {
                K5_Count++;  // 计数器递增
            }
        }
        else
        {
            K5_Count = 0;  // 已充满状态，计数器清零
        }
    }
    else  // 未充满电
    {
        if(K5_Press == 1)  // 如果之前是充满状态
        {
            if(K5_Count >= Key_Time)  // 达到消抖时间
            {
                K5_Press = 0;  // 标记为未充满状态
                K5_Count = 0;   // 计数器清零
            }
            else
            {
                K5_Count++;  // 计数器递增
            }
        }
        else
        {
            K5_Count = 0;  // 仍未充满状态，计数器清零
        }
    }
}


uint8_t Key_Buff_Return(void)
{
    Key_Buff = 0x00;

    if(K1_Press == 1) Key_Buff |= 0x01;  // K1
    if(K2_Press == 1) Key_Buff |= 0x02;  // K2
    if(K3_Press == 1) Key_Buff |= 0x04;  // K3
    if(K4_Press == 1) Key_Buff |= 0x08;  // Charging
    if(K5_Press == 1) Key_Buff |= 0x10;  // Full

    return Key_Buff;
}