C51 COMPILER V9.60.0.0   ADC_USED                                                          07/23/2025 14:15:03 PAGE 1   


C51 COMPILER V9.60.0.0, COMPILATION OF MODULE ADC_USED
OBJECT MODULE PLACED IN .\Objects\ADC_Used.obj
COMPILER INVOKED BY: D:\KeilC51\C51\BIN\C51.EXE ..\code\ADC_Used.c LARGE OMF2 OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Libary\
                    -Device\CMS8S6990\Include;..\Libary\Device\CMS8S6990;..\Libary\StdDriver\inc;..\Driver;..\code) DEBUG PRINT(.\Listings\AD
                    -C_Used.lst) TABS(2) OBJECT(.\Objects\ADC_Used.obj)

line level    source

   1          #include "ADC_Used.h"
   2          #include "define.h"
   3          #include <stdio.h>
   4          
   5          #define FILTER_SIZE 5  
   6          
   7          uint16_t ADC(uint8_t ADC_Channel) 
   8          {
   9   1      
  10   1          static uint16_t filter_buffer[FILTER_SIZE] = {0};
  11   1          static uint8_t filter_index = 0;
  12   1          uint32_t sum = 0;
  13   1          uint16_t adc_result;
  14   1          uint8_t i;
  15   1          float v_adc, v_bat;
  16   1      
  17   1          // ??????14
  18   1          ADC_Channel = ADC_CH_14;
  19   1          ADC_EnableChannel(ADC_Channel);
  20   1      
  21   1          ADC_GO();
  22   1          while (ADC_IS_BUSY);
  23   1          adc_result = ADC_GetADCResult();
  24   1      
  25   1          // ?????????
  26   1          filter_buffer[filter_index] = adc_result;
  27   1          filter_index = (filter_index + 1) % FILTER_SIZE;
  28   1          for (i = 0; i < FILTER_SIZE; i++) 
  29   1          {
  30   2              sum += filter_buffer[i];
  31   2          }
  32   1          adc_result = sum / FILTER_SIZE;
  33   1      
  34   1          // ?????????
  35   1          v_adc = adc_result * 2.4f / 4095.0f;
  36   1          v_bat = v_adc * (20.7f + 10.5f) / 10.5f;
  37   1      
  38   1          printf("ADC_CH14: %d (Vbat=%.2fV)\r\n", adc_result, v_bat);
  39   1          return adc_result;
  40   1      }
  41          
  42          // ??????ADC???????????
  43          void ADC_StartConvert(uint8_t ADC_Channel)
  44          {
  45   1          ADC_Channel = ADC_CH_14;  // ?????????14
  46   1          ADC_EnableChannel(ADC_Channel);
  47   1          ADC_GO();  // ???????
  48   1      }
  49          
  50          uint8_t ADC_GetConvertIntFlag(void)
  51          {
  52   1          return !ADC_IS_BUSY;  // ???????????1
  53   1      }
C51 COMPILER V9.60.0.0   ADC_USED                                                          07/23/2025 14:15:03 PAGE 2   

  54          
  55          void ADC_ClearConvertIntFlag(void)
  56          {
  57   1          // ADC????????????????????????????????
  58   1      }
  59          
  60          uint16_t ADC_GetResult(void)
  61          {
  62   1          return ADC_GetADCResult();
  63   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    338    ----
   CONSTANT SIZE    =     28    ----
   XDATA SIZE       =     11      15
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
