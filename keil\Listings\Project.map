LX51 LINKER/LOCATER V4.66.97.0                                                          07/22/2025  16:36:19  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   002268H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000A2H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000006H.2 BIT
C:000000H   C:000000H   C:00FFFFH   00000DH   CONST
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H.0 00001FH.7 000018H.0 ---    ---      **GAP**
000020H.0 000024H.4 000004H.5 BIT    UNIT     BIT            ?BI?MAIN
000024H.5 000025H.1 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000025H.2 000025H.5 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000025H.6 000026H.1 000000H.4 BIT    UNIT     BIT            _BIT_GROUP_
000026H.2 000026H   000000H.6 ---    ---      **GAP**
000027H   000027H   000001H   BYTE   UNIT     IDATA          ?STACK

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 3


* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000028H   000003H   BYTE   UNIT     CODE           ?PR?ADC_GETRESULT?ADC_USED
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000038H   000038H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000039H   000039H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
00003AH   00003AH   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   00003EH   000001H   BYTE   UNIT     CODE           ?PR?ADC_CLEARCONVERTINTFLAG?ADC_USED
00003FH   000042H   000004H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000061H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000062H   000062H   000001H   ---    ---      **GAP**
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   00008FH   00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCONVERTINTFLAG?ADC_USED
000090H   000092H   000003H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   0009ABH   0008F6H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
0009ACH   0011BDH   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
0011BEH   001328H   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
001329H   001452H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
001453H   00156DH   00011BH   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 4


00156EH   001686H   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
001687H   001796H   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
001797H   001887H   0000F1H   BYTE   UNIT     CODE           ?C_INITSEG
001888H   001952H   0000CBH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
001953H   0019ECH   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
0019EDH   001A7FH   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001A80H   001AF7H   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001AF8H   001B6FH   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001B70H   001BE2H   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
001BE3H   001C52H   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
001C53H   001CB1H   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
001CB2H   001D0EH   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
001D0FH   001D69H   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
001D6AH   001DB8H   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
001DB9H   001E04H   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
001E05H   001E4EH   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
001E4FH   001E95H   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
001E96H   001ED4H   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
001ED5H   001F0EH   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
001F0FH   001F43H   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
001F44H   001F77H   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
001F78H   001FA8H   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
001FA9H   001FD5H   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
001FD6H   001FFFH   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
002000H   002028H   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
002029H   00204FH   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
002050H   002075H   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
002076H   00209AH   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
00209BH   0020BFH   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
0020C0H   0020E3H   000024H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
0020E4H   002103H   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
002104H   002123H   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
002124H   002143H   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
002144H   002162H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
002163H   002181H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
002182H   0021A0H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
0021A1H   0021BEH   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
0021BFH   0021DBH   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
0021DCH   0021F5H   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
0021F6H   00220BH   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
00220CH   00221FH   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
002220H   002232H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
002233H   002245H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
002246H   002258H   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
002259H   002269H   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
00226AH   002272H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
002273H   00227BH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
00227CH   002284H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
002285H   00228DH   000009H   BYTE   UNIT     CODE           ?PR?_ADC_STARTCONVERT?ADC_USED
00228EH   002295H   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
002296H   00229BH   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
00229CH   0022A1H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
0022A2H   0022AEH   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   000032H   000033H   BYTE   UNIT     XDATA          ?XD?MAIN
000033H   000056H   000024H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000057H   000077H   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
000078H   00008CH   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
00008DH   000097H   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
000098H   00009DH   000006H   BYTE   UNIT     XDATA          ?XD?KEY
00009EH   0000A1H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 7


   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
   *DEL*:           00000FH   BYTE   UNIT     XDATA          ?XD?_ADC?ADC_USED
   *DEL*:           00001CH   BYTE   UNIT     CONST          ?CO?ADC_USED
   *DEL*:           000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
   *DEL*:           000005H   BYTE   UNIT     DATA           ?DT?PRINTF?PRINTF
   *DEL*:           000001H.1 BIT    UNIT     BIT            ?BI?PRINTF?PRINTF
   *DEL*:           000030H   BYTE   UNIT     XDATA          ?XD?PRINTF?PRINTF



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP
======================================================================
?C_C51STARTUP                                 ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     25H.6 26H.1  0033H 0043H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _TMR_STOP/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_START/TIMER
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 8


_DELAY1MS/MAIN                                ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----

_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  0044H 0049H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 9


  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH

FLASH_UNLOCK/FLASH                            ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  0044H 0045H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  0044H 0051H
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  0052H 0056H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----
  +--> _ADC_STARTCONVERT/ADC_USED
  +--> ADC_GETCONVERTINTFLAG/ADC_USED
  +--> ADC_GETRESULT/ADC_USED
  +--> ADC_CLEARCONVERTINTFLAG/ADC_USED

_ADC_STARTCONVERT/ADC_USED                    ----- -----  ----- -----
  +--> _ADC_ENABLECHANNEL/ADC

ADC_GETCONVERTINTFLAG/ADC_USED                ----- -----  ----- -----

ADC_GETRESULT/ADC_USED                        ----- -----  ----- -----
  +--> ADC_GETADCRESULT/ADC

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----

ADC_CLEARCONVERTINTFLAG/ADC_USED              ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----

_TMR_STOP/TIMER                               ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 10


  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----

?C_INITSEG                                    ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 11



*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

LSE_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
*DEL*:00000000H   XDATA    ---       ?_PRINTF?BYTE
*DEL*:00000000H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      02000052H   XDATA    BYTE      ?_UART_Send_String?BYTE
      01001198H   CODE     ---       ?C?CCASE
      01000F93H   CODE     ---       ?C?CLDOPTR
      01000F7AH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 12


      01000F54H   CODE     ---       ?C?COPY
      01000FD2H   CODE     ---       ?C?CSTOPTR
      01000FC0H   CODE     ---       ?C?CSTPTR
      01000B5CH   CODE     ---       ?C?FCASTC
      01000B57H   CODE     ---       ?C?FCASTI
      01000B52H   CODE     ---       ?C?FCASTL
      01000D23H   CODE     ---       ?C?FPADD
      01000C17H   CODE     ---       ?C?FPCONVERT
      01000AB5H   CODE     ---       ?C?FPDIV
      01000B90H   CODE     ---       ?C?FPGETOPN2
      010009ACH   CODE     ---       ?C?FPMUL
      01000BC5H   CODE     ---       ?C?FPNANRESULT
      01000BCFH   CODE     ---       ?C?FPOVERFLOW
      01000BA7H   CODE     ---       ?C?FPRESULT
      01000BBBH   CODE     ---       ?C?FPRESULT2
      01000BDAH   CODE     ---       ?C?FPROUND
      01000D1FH   CODE     ---       ?C?FPSUB
      01000BCCH   CODE     ---       ?C?FPUNDERFLOW
      01000E44H   CODE     ---       ?C?FTNPWR
      01001049H   CODE     ---       ?C?ILDIX
      0100112DH   CODE     ---       ?C?LNEG
      01001147H   CODE     ---       ?C?LSTKXDATA
      0100113BH   CODE     ---       ?C?LSTXDATA
      01001178H   CODE     ---       ?C?PLDIXDATA
      0100118FH   CODE     ---       ?C?PSTXDATA
      01000FF4H   CODE     ---       ?C?UIDIV
      0100109BH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      010019A8H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
*DEL*:00000000H   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      0100226AH   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      010021F6H   CODE     ---       _ADC_ConfigRunMode
      010021A1H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      01002285H   CODE     ---       _ADC_StartConvert
      01002124H   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 13


*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      01001F78H   CODE     ---       _FLASH_Erase
      01001F44H   CODE     ---       _FLASH_Read
      01001F0FH   CODE     ---       _FLASH_Write
*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001B70H   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      01001E4FH   CODE     ---       _Key_Function_Switch_System
      010019EDH   CODE     ---       _Motor_Step_Control
*DEL*:0000006BH   CODE     ---       _PRINTF
*DEL*:00000000H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
*DEL*:00000065H   CODE     ---       _SPRINTF
      01001FA9H   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      01001DB9H   CODE     ---       _TMR_ConfigRunMode
      01001E05H   CODE     ---       _TMR_ConfigTimerClk
      01002029H   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      010021BFH   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      01002144H   CODE     ---       _TMR_Start
      01002163H   CODE     ---       _TMR_Stop
      01002182H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      01002273H   CODE     ---       _UART_ConfigBRTClk
      0100227CH   CODE     ---       _UART_ConfigBRTPeriod
      01001C53H   CODE     ---       _UART_ConfigRunMode
      010020E4H   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      01002220H   CODE     ---       _UART_EnableDoubleFrequency
      01002259H   CODE     ---       _UART_EnableInt
      01002233H   CODE     ---       _UART_EnableReceive
      0100220CH   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      0100209BH   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01001CB2H   CODE     ---       _UART_Send_String
*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      01000030H   CODE     ---       ACMP_IRQHandler
      0100003EH   CODE     ---       ADC_ClearConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      010021DCH   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      01002076H   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
      01000086H   CODE     ---       ADC_GetConvertIntFlag
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000026H   CODE     ---       ADC_GetResult
      01000037H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000014H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.7 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000022H.0 BIT      BIT       batlow1
      0200002DH   XDATA    BYTE      batlow1_cnt
      0200001BH   XDATA    BYTE      batlow_cnt
      02000088H   XDATA    WORD      Battery_ADC_Wait_Time
      01001888H   CODE     ---       Battery_Check
      0200001AH   XDATA    BYTE      battery_check_divider
      0200002EH   XDATA    WORD      BatV
      00000023H.0 BIT      BIT       Bit_1_ms_Buff
      00000024H.0 BIT      BIT       Bit_N_ms_Buff
      00000023H.4 BIT      BIT       Bit_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 15


*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000025H.3 BIT      BIT       Center_Line_Control
      00000021H.6 BIT      BIT       Charg_State_Buff
      00000023H.2 BIT      BIT       charge_flash
      02000005H   XDATA    WORD      charge_flash_cnt
      00000022H.6 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      0100229CH   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      0200001CH   XDATA    INT       Count_1_Degree_Pulse
      02000083H   XDATA    INT       Count_Toggle
*SFR* 000000D0H.7 DATA     BIT       CY
      02000077H   XDATA    BYTE      Data_Length
      00000023H.3 BIT      BIT       Delay_Open
      00000025H.5 BIT      BIT       Delay_Over
      02000085H   XDATA    WORD      Delay_Time
      02000081H   XDATA    WORD      Delay_Time_Count
      00000024H.1 BIT      BIT       direction_changed
      02000022H   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000036H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000025H.4 BIT      BIT       Get_String_Buff
      02000087H   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      01001BE3H   CODE     ---       GPIO_Config
      01002246H   CODE     ---       GPIO_Key_Interrupt_Config
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 16


      01000039H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
      0200007AH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      02000099H   XDATA    BYTE      K1_Count
      00000024H.5 BIT      BIT       K1_Press
      0200007BH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      0200009AH   XDATA    BYTE      K2_Count
      00000023H.5 BIT      BIT       k2_long_press_detected
      0200001EH   XDATA    WORD      k2_long_press_timer
      00000024H.6 BIT      BIT       K2_Press
      00000022H.1 BIT      BIT       k2_released
      0200007CH   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      0200009BH   XDATA    BYTE      K3_Count
      00000024H.7 BIT      BIT       K3_Press
      00000022H.2 BIT      BIT       k3_released
      0200009CH   XDATA    BYTE      K4_Count
      00000025H.0 BIT      BIT       K4_Press
      0200009DH   XDATA    BYTE      K5_Count
      00000025H.1 BIT      BIT       K5_Press
      02000020H   XDATA    WORD      key1_duration
      00000022H.3 BIT      BIT       key1_handle
      00000023H.6 BIT      BIT       key1_long_started
      0200000CH   XDATA    WORD      key1_press_time
      00000022H.7 BIT      BIT       key1_pressed
      02000024H   XDATA    WORD      key3_duration
      00000022H.4 BIT      BIT       key3_handle
      00000023H.7 BIT      BIT       key3_long_started
      0200000EH   XDATA    WORD      key3_press_time
      00000023H.1 BIT      BIT       key3_pressed
      02000098H   XDATA    BYTE      Key_Buff
      01001ED5H   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010011BEH   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      01001329H   CODE     ---       Key_Scan
      0200000BH   XDATA    BYTE      key_scan_divider
      00000024H.2 BIT      BIT       key_short_press_mode
      0200002CH   XDATA    BYTE      last_direction
      01001687H   CODE     ---       LED_Control
      00000021H.5 BIT      BIT       led_flash_state
      02000018H   XDATA    WORD      led_flash_timer
      00000022H.5 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000017H   XDATA    BYTE      ledonoff1_cnt
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 17


      02000032H   XDATA    BYTE      ledonoff_cnt
      00000024H.3 BIT      BIT       longhit
      02000078H   XDATA    WORD      longhit_cnt
      0100002FH   CODE     ---       LSE_IRQHandler
      0100002EH   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
      02000016H   XDATA    BYTE      main_loop_counter
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      0200008CH   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      0200007FH   XDATA    WORD      Motor_Speed_Data
      00000021H.4 BIT      BIT       need_led_flash
      0200007DH   XDATA    INT       Num
      0200009EH   XDATA    INT       Num_Forward_Pulse
      020000A0H   XDATA    INT       Num_Reverse_Pulse
      02000029H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000029H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001A80H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001AF8H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 18


*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      0100002AH   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000025H.2 BIT      BIT       Power_count_clean
      0200008AH   XDATA    WORD      Power_Off_Wait_Time
      02000030H   XDATA    WORD      precise_k2_timer
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      01002050H   CODE     ---       Restore_dly
      01002296H   CODE     ---       Return_UART_Data_Length
*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000026H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.4 BIT      BIT       speedup
      02000010H   XDATA    WORD      speedup_cnt
      0100003AH   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000056H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      0200002BH   XDATA    BYTE      System_Mode_Before_Charge
      02000028H   XDATA    BYTE      System_Mode_Data
      02000007H   XDATA    DWORD     Systemclock
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 19


*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01001453H   CODE     ---       Timer0_IRQHandler
      010020C0H   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
      01000031H   CODE     ---       Timer3_IRQHandler
      01000032H   CODE     ---       Timer4_IRQHandler
      02000012H   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      01001FD6H   CODE     ---       TMR0_Config
      01002000H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      01001D0FH   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      01001D6AH   CODE     ---       UART_0_Config
      01001E96H   CODE     ---       UART_1_Config
      01002104H   CODE     ---       UART_Data_Init
      0100156EH   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      0100228EH   CODE     ---       UART_EnableBRT
      02000057H   XDATA    ---       UART_Get_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 20


      00000021H.3 BIT      BIT       use_precise_timer
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000038H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001956H   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01001953H   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      01001961H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01001953H   LINE      CODE     ---       #133
      01001955H   LINE      CODE     ---       #134
      01001956H   LINE      CODE     ---       #135
      01001957H   LINE      CODE     ---       #136
      01001959H   LINE      CODE     ---       #140
      0100195CH   LINE      CODE     ---       #141
      0100195EH   LINE      CODE     ---       #145
      01001960H   LINE      CODE     ---       #147
      01001961H   LINE      CODE     ---       #148
      01001962H   LINE      CODE     ---       #149
      01001963H   LINE      CODE     ---       #150
      01001965H   LINE      CODE     ---       #151
      01001967H   LINE      CODE     ---       #185
      0100196AH   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      0100226AH   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      01002076H   PUBLIC    CODE     ---       ADC_GetADCResult
      010021A1H   PUBLIC    CODE     ---       _ADC_EnableChannel
      010021F6H   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 21


      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 22


      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 23


      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      010021F6H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      010021F6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021F6H   LINE      CODE     ---       #88
      010021F6H   LINE      CODE     ---       #89
      010021F6H   LINE      CODE     ---       #90
      010021F6H   LINE      CODE     ---       #92
      010021F8H   LINE      CODE     ---       #93
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 24


      010021FBH   LINE      CODE     ---       #94
      010021FCH   LINE      CODE     ---       #95
      010021FEH   LINE      CODE     ---       #97
      01002200H   LINE      CODE     ---       #98
      01002204H   LINE      CODE     ---       #99
      01002209H   LINE      CODE     ---       #100
      0100220BH   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      010021A1H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      010021A1H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010021A1H   LINE      CODE     ---       #154
      010021A1H   LINE      CODE     ---       #155
      010021A1H   LINE      CODE     ---       #156
      010021A1H   LINE      CODE     ---       #158
      010021A3H   LINE      CODE     ---       #159
      010021A7H   LINE      CODE     ---       #160
      010021B0H   LINE      CODE     ---       #161
      010021B2H   LINE      CODE     ---       #163
      010021B4H   LINE      CODE     ---       #164
      010021B8H   LINE      CODE     ---       #165
      010021BCH   LINE      CODE     ---       #166
      010021BEH   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      01002076H   BLOCK     CODE     ---       LVL=0
      01002076H   LINE      CODE     ---       #258
      01002076H   LINE      CODE     ---       #259
      01002076H   LINE      CODE     ---       #260
      0100207DH   LINE      CODE     ---       #261
      0100207DH   LINE      CODE     ---       #262
      01002090H   LINE      CODE     ---       #263
      01002090H   LINE      CODE     ---       #264
      0100209AH   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      0100226AH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      0100226AH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100226AH   LINE      CODE     ---       #344
      0100226AH   LINE      CODE     ---       #345
      0100226AH   LINE      CODE     ---       #346
      0100226AH   LINE      CODE     ---       #348
      0100226EH   LINE      CODE     ---       #349
      01002270H   LINE      CODE     ---       #350
      01002271H   LINE      CODE     ---       #351
      01002272H   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 25


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 26


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 27


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 28


      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 29


      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 30


      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 31


      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000056H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 32


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 33


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 34


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      01000056H   LINE      CODE     ---       #358
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000057H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005BH   LINE      CODE     ---       #363
      0100005CH   LINE      CODE     ---       #364
      0100005DH   LINE      CODE     ---       #365
      0100005EH   LINE      CODE     ---       #366
      0100005FH   LINE      CODE     ---       #367
      01000060H   LINE      CODE     ---       #368
      01000061H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      01002163H   PUBLIC    CODE     ---       _TMR_Stop
      01002144H   PUBLIC    CODE     ---       _TMR_Start
      010021BFH   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      01002029H   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      01001E05H   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      01001DB9H   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 35


      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 36


      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 37


      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001DB9H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      01001DB9H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001DB9H   LINE      CODE     ---       #74
      01001DB9H   LINE      CODE     ---       #75
      01001DB9H   LINE      CODE     ---       #76
      01001DB9H   LINE      CODE     ---       #78
      01001DC8H   LINE      CODE     ---       #79
      01001DC8H   LINE      CODE     ---       #80
      01001DC8H   LINE      CODE     ---       #81
      01001DCAH   LINE      CODE     ---       #82
      01001DCEH   LINE      CODE     ---       #83
      01001DD4H   LINE      CODE     ---       #84
      01001DD4H   LINE      CODE     ---       #85
      01001DD6H   LINE      CODE     ---       #86
      01001DD6H   LINE      CODE     ---       #87
      01001DD8H   LINE      CODE     ---       #88
      01001DDCH   LINE      CODE     ---       #89
      01001DE9H   LINE      CODE     ---       #90
      01001DEBH   LINE      CODE     ---       #91
      01001DECH   LINE      CODE     ---       #92
      01001DECH   LINE      CODE     ---       #93
      01001DEEH   LINE      CODE     ---       #94
      01001DF1H   LINE      CODE     ---       #95
      01001DF2H   LINE      CODE     ---       #96
      01001DF4H   LINE      CODE     ---       #97
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 38


      01001DF5H   LINE      CODE     ---       #98
      01001DF5H   LINE      CODE     ---       #99
      01001DF7H   LINE      CODE     ---       #100
      01001DFBH   LINE      CODE     ---       #101
      01001E02H   LINE      CODE     ---       #102
      01001E04H   LINE      CODE     ---       #103
      01001E04H   LINE      CODE     ---       #104
      01001E04H   LINE      CODE     ---       #105
      01001E04H   LINE      CODE     ---       #106
      01001E04H   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      01001E05H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      01001E05H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001E05H   LINE      CODE     ---       #117
      01001E05H   LINE      CODE     ---       #118
      01001E05H   LINE      CODE     ---       #119
      01001E05H   LINE      CODE     ---       #121
      01001E14H   LINE      CODE     ---       #122
      01001E14H   LINE      CODE     ---       #123
      01001E14H   LINE      CODE     ---       #124
      01001E16H   LINE      CODE     ---       #125
      01001E1AH   LINE      CODE     ---       #126
      01001E20H   LINE      CODE     ---       #127
      01001E20H   LINE      CODE     ---       #128
      01001E22H   LINE      CODE     ---       #129
      01001E22H   LINE      CODE     ---       #130
      01001E24H   LINE      CODE     ---       #131
      01001E28H   LINE      CODE     ---       #132
      01001E2DH   LINE      CODE     ---       #133
      01001E2FH   LINE      CODE     ---       #134
      01001E30H   LINE      CODE     ---       #135
      01001E30H   LINE      CODE     ---       #136
      01001E32H   LINE      CODE     ---       #137
      01001E36H   LINE      CODE     ---       #138
      01001E3BH   LINE      CODE     ---       #139
      01001E3BH   LINE      CODE     ---       #140
      01001E3DH   LINE      CODE     ---       #141
      01001E3DH   LINE      CODE     ---       #142
      01001E3FH   LINE      CODE     ---       #143
      01001E43H   LINE      CODE     ---       #144
      01001E4CH   LINE      CODE     ---       #145
      01001E4EH   LINE      CODE     ---       #146
      01001E4EH   LINE      CODE     ---       #147
      01001E4EH   LINE      CODE     ---       #148
      01001E4EH   LINE      CODE     ---       #149
      01001E4EH   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01002029H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      01002029H   LINE      CODE     ---       #160
      01002029H   LINE      CODE     ---       #161
      01002029H   LINE      CODE     ---       #162
      01002038H   LINE      CODE     ---       #163
      01002038H   LINE      CODE     ---       #164
      01002038H   LINE      CODE     ---       #165
      0100203AH   LINE      CODE     ---       #166
      0100203CH   LINE      CODE     ---       #167
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 39


      0100203DH   LINE      CODE     ---       #168
      0100203DH   LINE      CODE     ---       #169
      0100203FH   LINE      CODE     ---       #170
      01002041H   LINE      CODE     ---       #171
      01002042H   LINE      CODE     ---       #172
      01002042H   LINE      CODE     ---       #173
      01002044H   LINE      CODE     ---       #174
      01002046H   LINE      CODE     ---       #175
      01002047H   LINE      CODE     ---       #176
      01002047H   LINE      CODE     ---       #177
      0100204BH   LINE      CODE     ---       #178
      0100204FH   LINE      CODE     ---       #179
      0100204FH   LINE      CODE     ---       #180
      0100204FH   LINE      CODE     ---       #181
      0100204FH   LINE      CODE     ---       #182
      0100204FH   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      010021BFH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010021BFH   LINE      CODE     ---       #256
      010021BFH   LINE      CODE     ---       #257
      010021BFH   LINE      CODE     ---       #258
      010021CEH   LINE      CODE     ---       #259
      010021CEH   LINE      CODE     ---       #260
      010021CEH   LINE      CODE     ---       #261
      010021D0H   LINE      CODE     ---       #262
      010021D1H   LINE      CODE     ---       #263
      010021D1H   LINE      CODE     ---       #264
      010021D3H   LINE      CODE     ---       #265
      010021D4H   LINE      CODE     ---       #266
      010021D4H   LINE      CODE     ---       #267
      010021D7H   LINE      CODE     ---       #268
      010021D8H   LINE      CODE     ---       #269
      010021D8H   LINE      CODE     ---       #270
      010021DBH   LINE      CODE     ---       #271
      010021DBH   LINE      CODE     ---       #272
      010021DBH   LINE      CODE     ---       #273
      010021DBH   LINE      CODE     ---       #274
      010021DBH   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002144H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002144H   LINE      CODE     ---       #368
      01002144H   LINE      CODE     ---       #369
      01002144H   LINE      CODE     ---       #370
      01002153H   LINE      CODE     ---       #371
      01002153H   LINE      CODE     ---       #372
      01002153H   LINE      CODE     ---       #373
      01002156H   LINE      CODE     ---       #374
      01002157H   LINE      CODE     ---       #375
      01002157H   LINE      CODE     ---       #376
      0100215AH   LINE      CODE     ---       #377
      0100215BH   LINE      CODE     ---       #378
      0100215BH   LINE      CODE     ---       #379
      0100215EH   LINE      CODE     ---       #380
      0100215FH   LINE      CODE     ---       #381
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 40


      0100215FH   LINE      CODE     ---       #382
      01002162H   LINE      CODE     ---       #383
      01002162H   LINE      CODE     ---       #384
      01002162H   LINE      CODE     ---       #385
      01002162H   LINE      CODE     ---       #386
      01002162H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0

      01002163H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002163H   LINE      CODE     ---       #395
      01002163H   LINE      CODE     ---       #396
      01002163H   LINE      CODE     ---       #397
      01002172H   LINE      CODE     ---       #398
      01002172H   LINE      CODE     ---       #399
      01002172H   LINE      CODE     ---       #400
      01002175H   LINE      CODE     ---       #401
      01002176H   LINE      CODE     ---       #402
      01002176H   LINE      CODE     ---       #403
      01002179H   LINE      CODE     ---       #404
      0100217AH   LINE      CODE     ---       #405
      0100217AH   LINE      CODE     ---       #406
      0100217DH   LINE      CODE     ---       #407
      0100217EH   LINE      CODE     ---       #408
      0100217EH   LINE      CODE     ---       #409
      01002181H   LINE      CODE     ---       #410
      01002181H   LINE      CODE     ---       #411
      01002181H   LINE      CODE     ---       #412
      01002181H   LINE      CODE     ---       #413
      01002181H   LINE      CODE     ---       #414
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      0100227CH   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      01002273H   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      0100228EH   PUBLIC    CODE     ---       UART_EnableBRT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 41


      0100220CH   PUBLIC    CODE     ---       _UART_GetBuff
      01002182H   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      0100209BH   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      01002259H   PUBLIC    CODE     ---       _UART_EnableInt
      01002233H   PUBLIC    CODE     ---       _UART_EnableReceive
      01002220H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      01001C53H   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 42


      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 43


      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001C53H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      01001C53H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01001C53H   LINE      CODE     ---       #70
      01001C53H   LINE      CODE     ---       #71
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 44


      01001C53H   LINE      CODE     ---       #72
      01001C53H   LINE      CODE     ---       #74
      01001C56H   LINE      CODE     ---       #75
      01001C56H   LINE      CODE     ---       #76
      01001C58H   LINE      CODE     ---       #77
      01001C5CH   LINE      CODE     ---       #78
      01001C63H   LINE      CODE     ---       #79
      01001C65H   LINE      CODE     ---       #81
      01001C68H   LINE      CODE     ---       #82
      01001C74H   LINE      CODE     ---       #83
      01001C74H   LINE      CODE     ---       #84
      01001C74H   LINE      CODE     ---       #85
      01001C74H   LINE      CODE     ---       #86
      01001C74H   LINE      CODE     ---       #87
      01001C77H   LINE      CODE     ---       #88
      01001C79H   LINE      CODE     ---       #89
      01001C79H   LINE      CODE     ---       #90
      01001C7CH   LINE      CODE     ---       #91
      01001C7EH   LINE      CODE     ---       #92
      01001C7EH   LINE      CODE     ---       #93
      01001C81H   LINE      CODE     ---       #94
      01001C81H   LINE      CODE     ---       #95
      01001C81H   LINE      CODE     ---       #96
      01001C81H   LINE      CODE     ---       #97
      01001C81H   LINE      CODE     ---       #99
      01001C81H   LINE      CODE     ---       #100
      01001C86H   LINE      CODE     ---       #101
      01001C86H   LINE      CODE     ---       #102
      01001C88H   LINE      CODE     ---       #103
      01001C8CH   LINE      CODE     ---       #104
      01001C95H   LINE      CODE     ---       #105
      01001C97H   LINE      CODE     ---       #107
      01001C9AH   LINE      CODE     ---       #108
      01001CA6H   LINE      CODE     ---       #109
      01001CA6H   LINE      CODE     ---       #110
      01001CA6H   LINE      CODE     ---       #111
      01001CA6H   LINE      CODE     ---       #112
      01001CA6H   LINE      CODE     ---       #113
      01001CA9H   LINE      CODE     ---       #114
      01001CAAH   LINE      CODE     ---       #115
      01001CAAH   LINE      CODE     ---       #116
      01001CADH   LINE      CODE     ---       #117
      01001CAEH   LINE      CODE     ---       #118
      01001CAEH   LINE      CODE     ---       #119
      01001CB1H   LINE      CODE     ---       #120
      01001CB1H   LINE      CODE     ---       #121
      01001CB1H   LINE      CODE     ---       #122
      01001CB1H   LINE      CODE     ---       #123
      01001CB1H   LINE      CODE     ---       #124
      01001CB1H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01002220H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002220H   LINE      CODE     ---       #133
      01002220H   LINE      CODE     ---       #134
      01002220H   LINE      CODE     ---       #135
      01002226H   LINE      CODE     ---       #136
      01002226H   LINE      CODE     ---       #137
      01002229H   LINE      CODE     ---       #138
      01002229H   LINE      CODE     ---       #139
      0100222FH   LINE      CODE     ---       #140
      0100222FH   LINE      CODE     ---       #141
      01002232H   LINE      CODE     ---       #142
      01002232H   LINE      CODE     ---       #143
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 45


      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002233H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002233H   LINE      CODE     ---       #280
      01002233H   LINE      CODE     ---       #281
      01002233H   LINE      CODE     ---       #282
      01002239H   LINE      CODE     ---       #283
      01002239H   LINE      CODE     ---       #284
      0100223CH   LINE      CODE     ---       #285
      0100223CH   LINE      CODE     ---       #286
      01002242H   LINE      CODE     ---       #287
      01002242H   LINE      CODE     ---       #288
      01002245H   LINE      CODE     ---       #289
      01002245H   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002259H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002259H   LINE      CODE     ---       #317
      01002259H   LINE      CODE     ---       #318
      01002259H   LINE      CODE     ---       #319
      0100225FH   LINE      CODE     ---       #320
      0100225FH   LINE      CODE     ---       #321
      01002261H   LINE      CODE     ---       #322
      01002261H   LINE      CODE     ---       #323
      01002267H   LINE      CODE     ---       #324
      01002267H   LINE      CODE     ---       #325
      01002269H   LINE      CODE     ---       #326
      01002269H   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100209BH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      0100209BH   LINE      CODE     ---       #353
      0100209DH   LINE      CODE     ---       #354
      0100209DH   LINE      CODE     ---       #355
      010020A3H   LINE      CODE     ---       #356
      010020A3H   LINE      CODE     ---       #357
      010020ADH   LINE      CODE     ---       #358
      010020ADH   LINE      CODE     ---       #359
      010020B3H   LINE      CODE     ---       #360
      010020B3H   LINE      CODE     ---       #361
      010020BDH   LINE      CODE     ---       #362
      010020BDH   LINE      CODE     ---       #363
      010020BFH   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      01002182H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002182H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002182H   LINE      CODE     ---       #373
      01002182H   LINE      CODE     ---       #374
      01002182H   LINE      CODE     ---       #377
      01002188H   LINE      CODE     ---       #378
      01002188H   LINE      CODE     ---       #379
      0100218AH   LINE      CODE     ---       #380
      0100218DH   LINE      CODE     ---       #381
      01002191H   LINE      CODE     ---       #382
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 46


      01002191H   LINE      CODE     ---       #383
      01002197H   LINE      CODE     ---       #384
      01002197H   LINE      CODE     ---       #385
      01002199H   LINE      CODE     ---       #386
      0100219CH   LINE      CODE     ---       #387
      010021A0H   LINE      CODE     ---       #388
      010021A0H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      0100220CH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100220CH   LINE      CODE     ---       #443
      0100220CH   LINE      CODE     ---       #444
      0100220CH   LINE      CODE     ---       #445
      01002212H   LINE      CODE     ---       #446
      01002212H   LINE      CODE     ---       #447
      01002215H   LINE      CODE     ---       #448
      01002215H   LINE      CODE     ---       #449
      0100221DH   LINE      CODE     ---       #450
      0100221DH   LINE      CODE     ---       #451
      0100221FH   LINE      CODE     ---       #452
      0100221FH   LINE      CODE     ---       #453
      0100221FH   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      0100228EH   BLOCK     CODE     ---       LVL=0
      0100228EH   LINE      CODE     ---       #534
      0100228EH   LINE      CODE     ---       #535
      0100228EH   LINE      CODE     ---       #536
      01002295H   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      01002273H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      01002273H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002273H   LINE      CODE     ---       #544
      01002273H   LINE      CODE     ---       #545
      01002273H   LINE      CODE     ---       #546
      01002273H   LINE      CODE     ---       #548
      01002277H   LINE      CODE     ---       #549
      01002279H   LINE      CODE     ---       #550
      0100227AH   LINE      CODE     ---       #551
      0100227BH   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      0100227CH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      0100227CH   LINE      CODE     ---       #560
      0100227CH   LINE      CODE     ---       #561
      0100227CH   LINE      CODE     ---       #562
      01002281H   LINE      CODE     ---       #563
      01002284H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 47


      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 48


      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 49


      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      01001F78H   PUBLIC    CODE     ---       _FLASH_Erase
      01001F44H   PUBLIC    CODE     ---       _FLASH_Read
      01001F0FH   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 50


      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 51


      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 52


      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      01001F0FH   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      01001F0FH   LINE      CODE     ---       #95
      01001F11H   LINE      CODE     ---       #96
      01001F11H   LINE      CODE     ---       #97
      01001F15H   LINE      CODE     ---       #98
      01001F17H   LINE      CODE     ---       #99
      01001F1AH   LINE      CODE     ---       #101
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 53


      01001F1DH   LINE      CODE     ---       #102
      01001F1DH   LINE      CODE     ---       #103
      01001F1FH   LINE      CODE     ---       #104
      01001F20H   LINE      CODE     ---       #105
      01001F25H   LINE      CODE     ---       #106
      01001F26H   LINE      CODE     ---       #107
      01001F27H   LINE      CODE     ---       #108
      01001F28H   LINE      CODE     ---       #109
      01001F29H   LINE      CODE     ---       #110
      01001F2AH   LINE      CODE     ---       #111
      01001F2BH   LINE      CODE     ---       #112
      01001F30H   LINE      CODE     ---       #113
      01001F32H   LINE      CODE     ---       #114
      01001F33H   LINE      CODE     ---       #116
      01001F33H   LINE      CODE     ---       #117
      01001F38H   LINE      CODE     ---       #118
      01001F39H   LINE      CODE     ---       #119
      01001F3AH   LINE      CODE     ---       #120
      01001F3BH   LINE      CODE     ---       #121
      01001F3CH   LINE      CODE     ---       #122
      01001F3DH   LINE      CODE     ---       #123
      01001F3EH   LINE      CODE     ---       #124
      01001F43H   LINE      CODE     ---       #125
      01001F43H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      01001F44H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F44H   LINE      CODE     ---       #138
      01001F46H   LINE      CODE     ---       #139
      01001F46H   LINE      CODE     ---       #140
      01001F48H   LINE      CODE     ---       #141
      01001F4BH   LINE      CODE     ---       #142
      01001F4EH   LINE      CODE     ---       #143
      01001F4EH   LINE      CODE     ---       #144
      01001F50H   LINE      CODE     ---       #145
      01001F51H   LINE      CODE     ---       #146
      01001F56H   LINE      CODE     ---       #147
      01001F57H   LINE      CODE     ---       #148
      01001F58H   LINE      CODE     ---       #149
      01001F59H   LINE      CODE     ---       #150
      01001F5AH   LINE      CODE     ---       #151
      01001F5BH   LINE      CODE     ---       #152
      01001F5CH   LINE      CODE     ---       #153
      01001F61H   LINE      CODE     ---       #154
      01001F63H   LINE      CODE     ---       #155
      01001F65H   LINE      CODE     ---       #157
      01001F65H   LINE      CODE     ---       #158
      01001F6AH   LINE      CODE     ---       #159
      01001F6BH   LINE      CODE     ---       #160
      01001F6CH   LINE      CODE     ---       #161
      01001F6DH   LINE      CODE     ---       #162
      01001F6EH   LINE      CODE     ---       #163
      01001F6FH   LINE      CODE     ---       #164
      01001F70H   LINE      CODE     ---       #165
      01001F75H   LINE      CODE     ---       #166
      01001F75H   LINE      CODE     ---       #167
      01001F77H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      01001F78H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      01001F78H   LINE      CODE     ---       #179
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 54


      01001F7AH   LINE      CODE     ---       #180
      01001F7AH   LINE      CODE     ---       #181
      01001F7CH   LINE      CODE     ---       #182
      01001F7FH   LINE      CODE     ---       #183
      01001F82H   LINE      CODE     ---       #184
      01001F82H   LINE      CODE     ---       #185
      01001F84H   LINE      CODE     ---       #186
      01001F85H   LINE      CODE     ---       #187
      01001F8AH   LINE      CODE     ---       #188
      01001F8BH   LINE      CODE     ---       #189
      01001F8CH   LINE      CODE     ---       #190
      01001F8DH   LINE      CODE     ---       #191
      01001F8EH   LINE      CODE     ---       #192
      01001F8FH   LINE      CODE     ---       #193
      01001F90H   LINE      CODE     ---       #194
      01001F95H   LINE      CODE     ---       #195
      01001F97H   LINE      CODE     ---       #196
      01001F98H   LINE      CODE     ---       #198
      01001F98H   LINE      CODE     ---       #199
      01001F9DH   LINE      CODE     ---       #200
      01001F9EH   LINE      CODE     ---       #201
      01001F9FH   LINE      CODE     ---       #202
      01001FA0H   LINE      CODE     ---       #203
      01001FA1H   LINE      CODE     ---       #204
      01001FA2H   LINE      CODE     ---       #205
      01001FA3H   LINE      CODE     ---       #206
      01001FA8H   LINE      CODE     ---       #207
      01001FA8H   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      010021DCH   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 55


      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 56


      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 57


      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010021DCH   BLOCK     CODE     ---       LVL=0
      010021DCH   LINE      CODE     ---       #65
      010021DCH   LINE      CODE     ---       #66
      010021DCH   LINE      CODE     ---       #68
      010021E3H   LINE      CODE     ---       #71
      010021E8H   LINE      CODE     ---       #72
      010021EEH   LINE      CODE     ---       #75
      010021F3H   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      0200008CH   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      0200008AH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      02000088H   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000025H.5 PUBLIC    BIT      BIT       Delay_Over
      02000087H   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      02000085H   PUBLIC    XDATA    WORD      Delay_Time
      00000025H.4 PUBLIC    BIT      BIT       Get_String_Buff
      02000083H   PUBLIC    XDATA    INT       Count_Toggle
      02000081H   PUBLIC    XDATA    WORD      Delay_Time_Count
      0200007FH   PUBLIC    XDATA    WORD      Motor_Speed_Data
      0200007DH   PUBLIC    XDATA    INT       Num
      0200007CH   PUBLIC    XDATA    BYTE      K3_cnt
      0200007BH   PUBLIC    XDATA    BYTE      K2_cnt
      0200007AH   PUBLIC    XDATA    BYTE      K1_cnt
      02000078H   PUBLIC    XDATA    WORD      longhit_cnt
      00000025H.3 PUBLIC    BIT      BIT       Center_Line_Control
      00000025H.2 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 58


      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 59


      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 60


      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      01002246H   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      01001BE3H   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 61


      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 62


      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 63


      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001BE3H   BLOCK     CODE     ---       LVL=0
      01001BE3H   LINE      CODE     ---       #42
      01001BE3H   LINE      CODE     ---       #43
      01001BE3H   LINE      CODE     ---       #44
      01001BE6H   LINE      CODE     ---       #45
      01001BE9H   LINE      CODE     ---       #46
      01001BECH   LINE      CODE     ---       #47
      01001BEFH   LINE      CODE     ---       #48
      01001BF2H   LINE      CODE     ---       #49
      01001BF5H   LINE      CODE     ---       #50
      01001BF8H   LINE      CODE     ---       #51
      01001BFBH   LINE      CODE     ---       #77
      01001C00H   LINE      CODE     ---       #78
      01001C03H   LINE      CODE     ---       #79
      01001C0AH   LINE      CODE     ---       #81
      01001C0FH   LINE      CODE     ---       #82
      01001C12H   LINE      CODE     ---       #83
      01001C19H   LINE      CODE     ---       #85
      01001C1EH   LINE      CODE     ---       #86
      01001C21H   LINE      CODE     ---       #87
      01001C28H   LINE      CODE     ---       #90
      01001C2DH   LINE      CODE     ---       #91
      01001C30H   LINE      CODE     ---       #92
      01001C37H   LINE      CODE     ---       #94
      01001C3CH   LINE      CODE     ---       #95
      01001C3FH   LINE      CODE     ---       #96
      01001C46H   LINE      CODE     ---       #100
      01001C4BH   LINE      CODE     ---       #101
      01001C4EH   LINE      CODE     ---       #102
      01001C50H   LINE      CODE     ---       #105
      01001C52H   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

      01002246H   BLOCK     CODE     ---       LVL=0
      01002246H   LINE      CODE     ---       #131
      01002246H   LINE      CODE     ---       #132
      01002246H   LINE      CODE     ---       #134
      0100224CH   LINE      CODE     ---       #135
      0100224FH   LINE      CODE     ---       #138
      01002253H   LINE      CODE     ---       #139
      01002256H   LINE      CODE     ---       #142
      01002258H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      01002000H   PUBLIC    CODE     ---       TMR1_Config
      01001FD6H   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 64


      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 65


      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 66


      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001FD6H   BLOCK     CODE     ---       LVL=0
      01001FD6H   LINE      CODE     ---       #11
      01001FD6H   LINE      CODE     ---       #12
      01001FD6H   LINE      CODE     ---       #16
      01001FDEH   LINE      CODE     ---       #20
      01001FE4H   LINE      CODE     ---       #24
      01001FEDH   LINE      CODE     ---       #29
      01001FF2H   LINE      CODE     ---       #34
      01001FF8H   LINE      CODE     ---       #35
      01001FFBH   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      01002000H   BLOCK     CODE     ---       LVL=0
      01002000H   LINE      CODE     ---       #50
      01002000H   LINE      CODE     ---       #51
      01002000H   LINE      CODE     ---       #55
      01002009H   LINE      CODE     ---       #59
      01002010H   LINE      CODE     ---       #63
      01002019H   LINE      CODE     ---       #68
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 67


      0100201EH   LINE      CODE     ---       #73
      01002021H   LINE      CODE     ---       #74
      01002024H   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      02000052H   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01001CB2H   PUBLIC    CODE     ---       _UART_Send_String
      01001E96H   PUBLIC    CODE     ---       UART_1_Config
      01001D6AH   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 68


      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 69


      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01001D6AH   BLOCK     CODE     ---       LVL=0
      01001D6AH   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001D6AH   LINE      CODE     ---       #42
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 70


      01001D6AH   LINE      CODE     ---       #43
      01001D6AH   LINE      CODE     ---       #44
      01001D74H   LINE      CODE     ---       #45
      01001D7CH   LINE      CODE     ---       #143
      01001D85H   LINE      CODE     ---       #144
      01001D8AH   LINE      CODE     ---       #147
      01001D8FH   LINE      CODE     ---       #148
      01001D94H   LINE      CODE     ---       #152
      01001D9FH   LINE      CODE     ---       #153
      01001DA2H   LINE      CODE     ---       #156
      01001DA8H   LINE      CODE     ---       #157
      01001DADH   LINE      CODE     ---       #159
      01001DB2H   LINE      CODE     ---       #160
      01001DB5H   LINE      CODE     ---       #161
      01001DB8H   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      01001E96H   BLOCK     CODE     ---       LVL=0
      01001E96H   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      BRTValue
      02000046H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      01001E96H   LINE      CODE     ---       #223
      01001E96H   LINE      CODE     ---       #224
      01001E96H   LINE      CODE     ---       #225
      01001EA0H   LINE      CODE     ---       #226
      01001EA8H   LINE      CODE     ---       #324
      01001EB1H   LINE      CODE     ---       #325
      01001EB6H   LINE      CODE     ---       #328
      01001EBBH   LINE      CODE     ---       #329
      01001EC0H   LINE      CODE     ---       #333
      01001ECBH   LINE      CODE     ---       #334
      01001ECEH   LINE      CODE     ---       #337
      01001ED4H   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      00000001H   SYMBOL    DATA     ---       s

      01001CB2H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000053H   SYMBOL    XDATA    ---       String
      02000056H   SYMBOL    XDATA    BYTE      Length
      01001CBDH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01001CB2H   LINE      CODE     ---       #408
      01001CBDH   LINE      CODE     ---       #409
      01001CBDH   LINE      CODE     ---       #410
      01001CBFH   LINE      CODE     ---       #411
      01001CC9H   LINE      CODE     ---       #412
      01001CC9H   LINE      CODE     ---       #413
      01001CCCH   LINE      CODE     ---       #414
      01001CCCH   LINE      CODE     ---       #415
      01001CE1H   LINE      CODE     ---       #416
      01001CE6H   LINE      CODE     ---       #417
      01001CE9H   LINE      CODE     ---       #418
      01001CE9H   LINE      CODE     ---       #419
      01001CEEH   LINE      CODE     ---       #420
      01001CEEH   LINE      CODE     ---       #421
      01001D03H   LINE      CODE     ---       #422
      01001D08H   LINE      CODE     ---       #423
      01001D0BH   LINE      CODE     ---       #424
      01001D0BH   LINE      CODE     ---       #425
      01001D0EH   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 71



      ---         MODULE    ---      ---       ISR
      020000A0H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      0200009EH   PUBLIC    XDATA    INT       Num_Forward_Pulse
      0100003AH   PUBLIC    CODE     ---       SPI_IRQHandler
      01000039H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000038H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000037H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000036H   PUBLIC    CODE     ---       EPWM_IRQHandler
      01000032H   PUBLIC    CODE     ---       Timer4_IRQHandler
      01000031H   PUBLIC    CODE     ---       Timer3_IRQHandler
      01000030H   PUBLIC    CODE     ---       ACMP_IRQHandler
      0100002FH   PUBLIC    CODE     ---       LSE_IRQHandler
      0100002EH   PUBLIC    CODE     ---       LVD_IRQHandler
      0100002AH   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001AF8H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001A80H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000029H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      01001D0FH   PUBLIC    CODE     ---       UART0_IRQHandler
      010020C0H   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01001453H   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 72


      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 73


      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 74


      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #73
      0100000AH   LINE      CODE     ---       #76
      ---         BLOCKEND  ---      ---       LVL=0

      01001453H   BLOCK     CODE     ---       LVL=0
      01001453H   LINE      CODE     ---       #85
      01001462H   LINE      CODE     ---       #87
      01001465H   LINE      CODE     ---       #88
      01001468H   LINE      CODE     ---       #90
      0100146AH   LINE      CODE     ---       #91
      01001478H   LINE      CODE     ---       #92
      01001486H   LINE      CODE     ---       #93
      0100149BH   LINE      CODE     ---       #95
      010014B7H   LINE      CODE     ---       #96
      010014B7H   LINE      CODE     ---       #97
      010014B7H   LINE      CODE     ---       #98
      010014B7H   LINE      CODE     ---       #99
      010014C3H   LINE      CODE     ---       #100
      010014C3H   LINE      CODE     ---       #101
      010014C3H   LINE      CODE     ---       #102
      010014C3H   LINE      CODE     ---       #103
      010014C3H   LINE      CODE     ---       #104
      010014C3H   LINE      CODE     ---       #105
      010014C5H   LINE      CODE     ---       #106
      010014C5H   LINE      CODE     ---       #107
      010014C5H   LINE      CODE     ---       #108
      010014D1H   LINE      CODE     ---       #109
      010014D1H   LINE      CODE     ---       #110
      010014D1H   LINE      CODE     ---       #111
      010014D1H   LINE      CODE     ---       #112
      010014D1H   LINE      CODE     ---       #113
      010014D1H   LINE      CODE     ---       #114
      010014D3H   LINE      CODE     ---       #115
      010014D3H   LINE      CODE     ---       #116
      010014D3H   LINE      CODE     ---       #117
      010014DFH   LINE      CODE     ---       #118
      010014DFH   LINE      CODE     ---       #119
      010014DFH   LINE      CODE     ---       #120
      010014DFH   LINE      CODE     ---       #121
      010014DFH   LINE      CODE     ---       #122
      010014DFH   LINE      CODE     ---       #123
      010014E1H   LINE      CODE     ---       #124
      010014E1H   LINE      CODE     ---       #125
      010014E1H   LINE      CODE     ---       #126
      010014EDH   LINE      CODE     ---       #127
      010014EDH   LINE      CODE     ---       #128
      010014EFH   LINE      CODE     ---       #129
      010014F5H   LINE      CODE     ---       #130
      010014F5H   LINE      CODE     ---       #131
      010014F5H   LINE      CODE     ---       #132
      010014F5H   LINE      CODE     ---       #133
      010014F5H   LINE      CODE     ---       #134
      010014F5H   LINE      CODE     ---       #135
      010014F5H   LINE      CODE     ---       #137
      010014F8H   LINE      CODE     ---       #138
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 75


      010014F8H   LINE      CODE     ---       #139
      01001501H   LINE      CODE     ---       #140
      01001503H   LINE      CODE     ---       #142
      01001503H   LINE      CODE     ---       #143
      01001508H   LINE      CODE     ---       #144
      01001508H   LINE      CODE     ---       #146
      01001515H   LINE      CODE     ---       #147
      01001515H   LINE      CODE     ---       #148
      01001517H   LINE      CODE     ---       #149
      01001525H   LINE      CODE     ---       #150
      01001527H   LINE      CODE     ---       #152
      01001527H   LINE      CODE     ---       #153
      01001529H   LINE      CODE     ---       #154
      01001530H   LINE      CODE     ---       #155
      01001530H   LINE      CODE     ---       #157
      01001533H   LINE      CODE     ---       #158
      01001533H   LINE      CODE     ---       #159
      01001541H   LINE      CODE     ---       #160
      01001556H   LINE      CODE     ---       #161
      01001556H   LINE      CODE     ---       #162
      01001558H   LINE      CODE     ---       #163
      01001558H   LINE      CODE     ---       #164
      01001558H   LINE      CODE     ---       #165
      0100155AH   LINE      CODE     ---       #167
      0100155AH   LINE      CODE     ---       #168
      01001561H   LINE      CODE     ---       #169
      01001561H   LINE      CODE     ---       #172
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #181
      01000012H   LINE      CODE     ---       #184
      ---         BLOCKEND  ---      ---       LVL=0

      010020C0H   BLOCK     CODE     ---       LVL=0
      010020C0H   LINE      CODE     ---       #193
      010020C6H   LINE      CODE     ---       #196
      010020C9H   LINE      CODE     ---       #197
      010020CCH   LINE      CODE     ---       #200
      010020CFH   LINE      CODE     ---       #201
      010020CFH   LINE      CODE     ---       #202
      010020DDH   LINE      CODE     ---       #203
      010020DDH   LINE      CODE     ---       #204
      ---         BLOCKEND  ---      ---       LVL=0

      01001D0FH   BLOCK     CODE     ---       LVL=0
      01001D0FH   LINE      CODE     ---       #213
      01001D2CH   LINE      CODE     ---       #215
      01001D34H   LINE      CODE     ---       #216
      01001D34H   LINE      CODE     ---       #217
      01001D36H   LINE      CODE     ---       #218
      01001D3BH   LINE      CODE     ---       #219
      01001D4AH   LINE      CODE     ---       #220
      01001D4FH   LINE      CODE     ---       #221
      01001D4FH   LINE      CODE     ---       #222
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #231
      0100001AH   LINE      CODE     ---       #234
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #243
      01000022H   LINE      CODE     ---       #246
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 76


      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #255
      01000029H   LINE      CODE     ---       #258
      ---         BLOCKEND  ---      ---       LVL=0

      01001A80H   BLOCK     CODE     ---       LVL=0
      01001A80H   LINE      CODE     ---       #267
      01001A8FH   LINE      CODE     ---       #270
      01001A92H   LINE      CODE     ---       #273
      01001A95H   LINE      CODE     ---       #274
      01001A95H   LINE      CODE     ---       #276
      01001A98H   LINE      CODE     ---       #277
      01001A98H   LINE      CODE     ---       #279
      01001AA7H   LINE      CODE     ---       #281
      01001AA9H   LINE      CODE     ---       #283
      01001AABH   LINE      CODE     ---       #284
      01001AABH   LINE      CODE     ---       #285
      01001AADH   LINE      CODE     ---       #287
      01001AADH   LINE      CODE     ---       #289
      01001AB0H   LINE      CODE     ---       #290
      01001AB0H   LINE      CODE     ---       #292
      01001ACBH   LINE      CODE     ---       #294
      01001AE7H   LINE      CODE     ---       #295
      01001AE7H   LINE      CODE     ---       #297
      01001AE9H   LINE      CODE     ---       #298
      01001AE9H   LINE      CODE     ---       #300
      01001AEBH   LINE      CODE     ---       #301
      01001AEBH   LINE      CODE     ---       #302
      01001AEBH   LINE      CODE     ---       #303
      ---         BLOCKEND  ---      ---       LVL=0

      01001AF8H   BLOCK     CODE     ---       LVL=0
      01001AF8H   LINE      CODE     ---       #312
      01001B07H   LINE      CODE     ---       #314
      01001B0AH   LINE      CODE     ---       #316
      01001B0DH   LINE      CODE     ---       #317
      01001B0DH   LINE      CODE     ---       #319
      01001B10H   LINE      CODE     ---       #320
      01001B10H   LINE      CODE     ---       #321
      01001B1FH   LINE      CODE     ---       #322
      01001B21H   LINE      CODE     ---       #323
      01001B23H   LINE      CODE     ---       #324
      01001B23H   LINE      CODE     ---       #325
      01001B25H   LINE      CODE     ---       #327
      01001B25H   LINE      CODE     ---       #328
      01001B28H   LINE      CODE     ---       #329
      01001B28H   LINE      CODE     ---       #330
      01001B43H   LINE      CODE     ---       #331
      01001B5FH   LINE      CODE     ---       #332
      01001B5FH   LINE      CODE     ---       #333
      01001B61H   LINE      CODE     ---       #334
      01001B61H   LINE      CODE     ---       #335
      01001B63H   LINE      CODE     ---       #336
      01001B63H   LINE      CODE     ---       #337
      01001B63H   LINE      CODE     ---       #338
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #347
      0100002AH   LINE      CODE     ---       #350
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 77


      0100002EH   LINE      CODE     ---       #359
      0100002EH   LINE      CODE     ---       #362
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #371
      0100002FH   LINE      CODE     ---       #374
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #383
      01000030H   LINE      CODE     ---       #386
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #395
      01000031H   LINE      CODE     ---       #398
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #407
      01000032H   LINE      CODE     ---       #410
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #419
      01000036H   LINE      CODE     ---       #422
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #431
      01000037H   LINE      CODE     ---       #434
      ---         BLOCKEND  ---      ---       LVL=0

      01000038H   BLOCK     CODE     ---       LVL=0
      01000038H   LINE      CODE     ---       #443
      01000038H   LINE      CODE     ---       #446
      ---         BLOCKEND  ---      ---       LVL=0

      01000039H   BLOCK     CODE     ---       LVL=0
      01000039H   LINE      CODE     ---       #455
      01000039H   LINE      CODE     ---       #458
      ---         BLOCKEND  ---      ---       LVL=0

      0100003AH   BLOCK     CODE     ---       LVL=0
      0100003AH   LINE      CODE     ---       #467
      0100003AH   LINE      CODE     ---       #470
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      02000077H   PUBLIC    XDATA    BYTE      Data_Length
      02000057H   PUBLIC    XDATA    ---       UART_Get_String
      0100156EH   PUBLIC    CODE     ---       UART_Data_Process
      01001B70H   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      01002104H   PUBLIC    CODE     ---       UART_Data_Init
      0100229CH   PUBLIC    CODE     ---       Clean_UART_Data_Length
      01002296H   PUBLIC    CODE     ---       Return_UART_Data_Length
      010020E4H   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 78


      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 79


      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 80


      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      010022A2H   SYMBOL    CONST    ---       _?ix1000
      010022A5H   SYMBOL    CONST    ---       _?ix1001
      010022A8H   SYMBOL    CONST    ---       _?ix1002

      010020E4H   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      010020E4H   LINE      CODE     ---       #12
      010020E4H   LINE      CODE     ---       #13
      010020E4H   LINE      CODE     ---       #14
      010020F1H   LINE      CODE     ---       #15
      010020F7H   LINE      CODE     ---       #16
      01002101H   LINE      CODE     ---       #17
      01002101H   LINE      CODE     ---       #18
      01002103H   LINE      CODE     ---       #19
      01002103H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      01002296H   BLOCK     CODE     ---       LVL=0
      01002296H   LINE      CODE     ---       #24
      01002296H   LINE      CODE     ---       #25
      01002296H   LINE      CODE     ---       #26
      0100229BH   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 81



      0100229CH   BLOCK     CODE     ---       LVL=0
      0100229CH   LINE      CODE     ---       #30
      0100229CH   LINE      CODE     ---       #31
      0100229CH   LINE      CODE     ---       #32
      010022A1H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      01002104H   BLOCK     CODE     ---       LVL=0
      01002104H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01002104H   LINE      CODE     ---       #36
      01002104H   LINE      CODE     ---       #37
      01002104H   LINE      CODE     ---       #39
      01002109H   LINE      CODE     ---       #40
      01002114H   LINE      CODE     ---       #41
      01002114H   LINE      CODE     ---       #42
      01002120H   LINE      CODE     ---       #43
      01002123H   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001B70H   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    BYTE      CMD_No
      01001B75H   BLOCK     CODE     NEAR LAB  LVL=1
      02000045H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000048H   SYMBOL    XDATA    ---       UART_Error_Data
      0200004BH   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001B70H   LINE      CODE     ---       #61
      01001B75H   LINE      CODE     ---       #62
      01001B75H   LINE      CODE     ---       #63
      01001B88H   LINE      CODE     ---       #64
      01001B9BH   LINE      CODE     ---       #65
      01001BAEH   LINE      CODE     ---       #66
      01001BBCH   LINE      CODE     ---       #67
      01001BBCH   LINE      CODE     ---       #69
      01001BBCH   LINE      CODE     ---       #70
      01001BBCH   LINE      CODE     ---       #71
      01001BC2H   LINE      CODE     ---       #72
      01001BC2H   LINE      CODE     ---       #73
      01001BC4H   LINE      CODE     ---       #75
      01001BC4H   LINE      CODE     ---       #76
      01001BC4H   LINE      CODE     ---       #77
      01001BCFH   LINE      CODE     ---       #78
      01001BCFH   LINE      CODE     ---       #79
      01001BD1H   LINE      CODE     ---       #81
      01001BD1H   LINE      CODE     ---       #82
      01001BD1H   LINE      CODE     ---       #83
      01001BE2H   LINE      CODE     ---       #86
      01001BE2H   LINE      CODE     ---       #87
      01001BE2H   LINE      CODE     ---       #88
      01001BE2H   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      0100156EH   BLOCK     CODE     ---       LVL=0
      0100156EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      0100156EH   LINE      CODE     ---       #92
      0100156EH   LINE      CODE     ---       #93
      0100156EH   LINE      CODE     ---       #96
      01001571H   LINE      CODE     ---       #98
      01001576H   LINE      CODE     ---       #99
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 82


      01001576H   LINE      CODE     ---       #100
      01001579H   LINE      CODE     ---       #101
      0100157CH   LINE      CODE     ---       #102
      0100157CH   LINE      CODE     ---       #104
      01001587H   LINE      CODE     ---       #105
      01001587H   LINE      CODE     ---       #106
      01001596H   LINE      CODE     ---       #107
      01001596H   LINE      CODE     ---       #109
      01001598H   LINE      CODE     ---       #110
      0100159BH   LINE      CODE     ---       #111
      010015A9H   LINE      CODE     ---       #112
      010015A9H   LINE      CODE     ---       #114
      010015ABH   LINE      CODE     ---       #115
      010015AEH   LINE      CODE     ---       #116
      010015BCH   LINE      CODE     ---       #117
      010015BCH   LINE      CODE     ---       #119
      010015BEH   LINE      CODE     ---       #120
      010015C1H   LINE      CODE     ---       #121
      010015D2H   LINE      CODE     ---       #122
      010015D2H   LINE      CODE     ---       #124
      010015D4H   LINE      CODE     ---       #125
      010015D7H   LINE      CODE     ---       #126
      010015E5H   LINE      CODE     ---       #127
      010015E5H   LINE      CODE     ---       #129
      010015E7H   LINE      CODE     ---       #130
      010015EAH   LINE      CODE     ---       #131
      010015F8H   LINE      CODE     ---       #132
      010015F8H   LINE      CODE     ---       #134
      010015FAH   LINE      CODE     ---       #135
      010015FDH   LINE      CODE     ---       #136
      0100160EH   LINE      CODE     ---       #137
      0100160EH   LINE      CODE     ---       #139
      01001610H   LINE      CODE     ---       #140
      01001612H   LINE      CODE     ---       #141
      01001620H   LINE      CODE     ---       #142
      01001620H   LINE      CODE     ---       #144
      01001622H   LINE      CODE     ---       #145
      01001624H   LINE      CODE     ---       #146
      01001632H   LINE      CODE     ---       #147
      01001632H   LINE      CODE     ---       #149
      01001634H   LINE      CODE     ---       #150
      01001636H   LINE      CODE     ---       #151
      01001647H   LINE      CODE     ---       #152
      01001647H   LINE      CODE     ---       #154
      01001649H   LINE      CODE     ---       #155
      0100164BH   LINE      CODE     ---       #156
      01001659H   LINE      CODE     ---       #157
      01001659H   LINE      CODE     ---       #159
      0100165BH   LINE      CODE     ---       #160
      0100165DH   LINE      CODE     ---       #161
      0100166BH   LINE      CODE     ---       #162
      0100166BH   LINE      CODE     ---       #164
      0100166DH   LINE      CODE     ---       #165
      0100166FH   LINE      CODE     ---       #166
      0100167BH   LINE      CODE     ---       #167
      0100167BH   LINE      CODE     ---       #169
      0100167DH   LINE      CODE     ---       #170
      0100167FH   LINE      CODE     ---       #172
      0100167FH   LINE      CODE     ---       #174
      01001681H   LINE      CODE     ---       #175
      01001681H   LINE      CODE     ---       #176
      01001681H   LINE      CODE     ---       #178
      01001684H   LINE      CODE     ---       #180
      01001686H   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 83



      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000025H.1 PUBLIC    BIT      BIT       K5_Press
      00000025H.0 PUBLIC    BIT      BIT       K4_Press
      00000024H.7 PUBLIC    BIT      BIT       K3_Press
      00000024H.6 PUBLIC    BIT      BIT       K2_Press
      00000024H.5 PUBLIC    BIT      BIT       K1_Press
      0200009DH   PUBLIC    XDATA    BYTE      K5_Count
      0200009CH   PUBLIC    XDATA    BYTE      K4_Count
      0200009BH   PUBLIC    XDATA    BYTE      K3_Count
      0200009AH   PUBLIC    XDATA    BYTE      K2_Count
      02000099H   PUBLIC    XDATA    BYTE      K1_Count
      02000098H   PUBLIC    XDATA    BYTE      Key_Buff
      01001ED5H   PUBLIC    CODE     ---       Key_Buff_Return
      01001329H   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 84


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 85


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 86



      01001329H   BLOCK     CODE     ---       LVL=0
      01001329H   LINE      CODE     ---       #20
      01001329H   LINE      CODE     ---       #21
      01001329H   LINE      CODE     ---       #23
      0100132CH   LINE      CODE     ---       #24
      0100132CH   LINE      CODE     ---       #25
      0100132FH   LINE      CODE     ---       #26
      0100132FH   LINE      CODE     ---       #27
      0100133CH   LINE      CODE     ---       #28
      0100133CH   LINE      CODE     ---       #29
      0100133EH   LINE      CODE     ---       #30
      01001340H   LINE      CODE     ---       #31
      01001342H   LINE      CODE     ---       #43
      01001342H   LINE      CODE     ---       #44
      01001345H   LINE      CODE     ---       #45
      01001345H   LINE      CODE     ---       #46
      01001352H   LINE      CODE     ---       #47
      01001352H   LINE      CODE     ---       #48
      01001354H   LINE      CODE     ---       #49
      01001356H   LINE      CODE     ---       #50
      01001358H   LINE      CODE     ---       #52
      01001358H   LINE      CODE     ---       #53
      0100135EH   LINE      CODE     ---       #54
      0100135EH   LINE      CODE     ---       #55
      01001360H   LINE      CODE     ---       #57
      01001360H   LINE      CODE     ---       #58
      01001365H   LINE      CODE     ---       #59
      01001365H   LINE      CODE     ---       #60
      01001365H   LINE      CODE     ---       #63
      01001368H   LINE      CODE     ---       #64
      01001368H   LINE      CODE     ---       #65
      0100136BH   LINE      CODE     ---       #66
      0100136BH   LINE      CODE     ---       #67
      01001378H   LINE      CODE     ---       #68
      01001378H   LINE      CODE     ---       #69
      0100137AH   LINE      CODE     ---       #70
      0100137CH   LINE      CODE     ---       #71
      0100137EH   LINE      CODE     ---       #83
      0100137EH   LINE      CODE     ---       #84
      01001381H   LINE      CODE     ---       #85
      01001381H   LINE      CODE     ---       #86
      0100138EH   LINE      CODE     ---       #87
      0100138EH   LINE      CODE     ---       #88
      01001390H   LINE      CODE     ---       #89
      01001392H   LINE      CODE     ---       #90
      01001394H   LINE      CODE     ---       #92
      01001394H   LINE      CODE     ---       #93
      0100139AH   LINE      CODE     ---       #94
      0100139AH   LINE      CODE     ---       #95
      0100139CH   LINE      CODE     ---       #97
      0100139CH   LINE      CODE     ---       #98
      010013A1H   LINE      CODE     ---       #99
      010013A1H   LINE      CODE     ---       #100
      010013A1H   LINE      CODE     ---       #103
      010013A4H   LINE      CODE     ---       #104
      010013A4H   LINE      CODE     ---       #105
      010013A7H   LINE      CODE     ---       #106
      010013A7H   LINE      CODE     ---       #107
      010013B4H   LINE      CODE     ---       #108
      010013B4H   LINE      CODE     ---       #109
      010013B6H   LINE      CODE     ---       #110
      010013B8H   LINE      CODE     ---       #111
      010013BAH   LINE      CODE     ---       #123
      010013BAH   LINE      CODE     ---       #124
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 87


      010013BDH   LINE      CODE     ---       #125
      010013BDH   LINE      CODE     ---       #126
      010013CAH   LINE      CODE     ---       #127
      010013CAH   LINE      CODE     ---       #128
      010013CCH   LINE      CODE     ---       #129
      010013CEH   LINE      CODE     ---       #130
      010013D0H   LINE      CODE     ---       #132
      010013D0H   LINE      CODE     ---       #133
      010013D6H   LINE      CODE     ---       #134
      010013D6H   LINE      CODE     ---       #135
      010013D8H   LINE      CODE     ---       #137
      010013D8H   LINE      CODE     ---       #138
      010013DDH   LINE      CODE     ---       #139
      010013DDH   LINE      CODE     ---       #140
      010013DDH   LINE      CODE     ---       #143
      010013E0H   LINE      CODE     ---       #144
      010013E0H   LINE      CODE     ---       #145
      010013E3H   LINE      CODE     ---       #146
      010013E3H   LINE      CODE     ---       #147
      010013F0H   LINE      CODE     ---       #148
      010013F0H   LINE      CODE     ---       #149
      010013F2H   LINE      CODE     ---       #150
      010013F4H   LINE      CODE     ---       #151
      010013F6H   LINE      CODE     ---       #163
      010013F6H   LINE      CODE     ---       #164
      010013F9H   LINE      CODE     ---       #165
      010013F9H   LINE      CODE     ---       #166
      01001406H   LINE      CODE     ---       #167
      01001406H   LINE      CODE     ---       #168
      01001408H   LINE      CODE     ---       #169
      0100140AH   LINE      CODE     ---       #170
      0100140CH   LINE      CODE     ---       #172
      0100140CH   LINE      CODE     ---       #173
      01001412H   LINE      CODE     ---       #174
      01001412H   LINE      CODE     ---       #175
      01001414H   LINE      CODE     ---       #177
      01001414H   LINE      CODE     ---       #178
      01001419H   LINE      CODE     ---       #179
      01001419H   LINE      CODE     ---       #180
      01001419H   LINE      CODE     ---       #183
      0100141CH   LINE      CODE     ---       #184
      0100141CH   LINE      CODE     ---       #185
      0100141FH   LINE      CODE     ---       #186
      0100141FH   LINE      CODE     ---       #187
      0100142CH   LINE      CODE     ---       #188
      0100142CH   LINE      CODE     ---       #189
      0100142EH   LINE      CODE     ---       #190
      01001430H   LINE      CODE     ---       #191
      01001431H   LINE      CODE     ---       #203
      01001431H   LINE      CODE     ---       #204
      01001434H   LINE      CODE     ---       #205
      01001434H   LINE      CODE     ---       #206
      01001441H   LINE      CODE     ---       #207
      01001441H   LINE      CODE     ---       #208
      01001443H   LINE      CODE     ---       #209
      01001445H   LINE      CODE     ---       #210
      01001446H   LINE      CODE     ---       #212
      01001446H   LINE      CODE     ---       #213
      0100144CH   LINE      CODE     ---       #214
      0100144CH   LINE      CODE     ---       #215
      0100144DH   LINE      CODE     ---       #217
      0100144DH   LINE      CODE     ---       #218
      01001452H   LINE      CODE     ---       #219
      01001452H   LINE      CODE     ---       #220
      01001452H   LINE      CODE     ---       #221
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 88


      ---         BLOCKEND  ---      ---       LVL=0

      01001ED5H   BLOCK     CODE     ---       LVL=0
      01001ED5H   LINE      CODE     ---       #224
      01001ED5H   LINE      CODE     ---       #225
      01001ED5H   LINE      CODE     ---       #226
      01001EDAH   LINE      CODE     ---       #228
      01001EE1H   LINE      CODE     ---       #229
      01001EEBH   LINE      CODE     ---       #230
      01001EF5H   LINE      CODE     ---       #231
      01001EFFH   LINE      CODE     ---       #232
      01001F09H   LINE      CODE     ---       #234
      01001F0EH   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      01000026H   PUBLIC    CODE     ---       ADC_GetResult
      0100003EH   PUBLIC    CODE     ---       ADC_ClearConvertIntFlag
      01000086H   PUBLIC    CODE     ---       ADC_GetConvertIntFlag
      01002285H   PUBLIC    CODE     ---       _ADC_StartConvert
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 89


      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 90


      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 91


      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0200008DH   SYMBOL    XDATA    ---       filter_buffer
      02000097H   SYMBOL    XDATA    BYTE      filter_index

      01002285H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      01002285H   LINE      CODE     ---       #43
      01002285H   LINE      CODE     ---       #44
      01002285H   LINE      CODE     ---       #45
      01002287H   LINE      CODE     ---       #46
      0100228AH   LINE      CODE     ---       #47
      0100228DH   LINE      CODE     ---       #48
      ---         BLOCKEND  ---      ---       LVL=0

      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #50
      01000086H   LINE      CODE     ---       #51
      01000086H   LINE      CODE     ---       #52
      0100008FH   LINE      CODE     ---       #53
      ---         BLOCKEND  ---      ---       LVL=0

      0100003EH   BLOCK     CODE     ---       LVL=0
      0100003EH   LINE      CODE     ---       #55
      0100003EH   LINE      CODE     ---       #56
      0100003EH   LINE      CODE     ---       #58
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #60
      01000026H   LINE      CODE     ---       #61
      01000026H   LINE      CODE     ---       #62
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.4 PUBLIC    BIT      BIT       speedup
      00000024H.3 PUBLIC    BIT      BIT       longhit
      02000032H   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.2 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.1 PUBLIC    BIT      BIT       direction_changed
      00000024H.0 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000030H   PUBLIC    XDATA    WORD      precise_k2_timer
      0200002EH   PUBLIC    XDATA    WORD      BatV
      00000023H.7 PUBLIC    BIT      BIT       key3_long_started
      00000023H.6 PUBLIC    BIT      BIT       key1_long_started
      00000023H.5 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.4 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.3 PUBLIC    BIT      BIT       Delay_Open
      0200002DH   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.2 PUBLIC    BIT      BIT       charge_flash
      0200002CH   PUBLIC    XDATA    BYTE      last_direction
      00000023H.1 PUBLIC    BIT      BIT       key3_pressed
      00000023H.0 PUBLIC    BIT      BIT       Bit_1_ms_Buff
      00000022H.7 PUBLIC    BIT      BIT       key1_pressed
      0200002BH   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.6 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.5 PUBLIC    BIT      BIT       ledonoff
      02000029H   PUBLIC    XDATA    WORD      original_speed
      00000022H.4 PUBLIC    BIT      BIT       key3_handle
      02000028H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000026H   PUBLIC    XDATA    INT       Self_Check
      00000022H.3 PUBLIC    BIT      BIT       key1_handle
      02000024H   PUBLIC    XDATA    WORD      key3_duration
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 92


      02000022H   PUBLIC    XDATA    WORD      dly
      00000022H.2 PUBLIC    BIT      BIT       k3_released
      02000020H   PUBLIC    XDATA    WORD      key1_duration
      00000022H.1 PUBLIC    BIT      BIT       k2_released
      0200001EH   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000022H.0 PUBLIC    BIT      BIT       batlow1
      0200001CH   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.7 PUBLIC    BIT      BIT       auto_rotate_mode
      0200001BH   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.6 PUBLIC    BIT      BIT       Charg_State_Buff
      0200001AH   PUBLIC    XDATA    BYTE      battery_check_divider
      00000021H.5 PUBLIC    BIT      BIT       led_flash_state
      02000018H   PUBLIC    XDATA    WORD      led_flash_timer
      02000017H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      02000016H   PUBLIC    XDATA    BYTE      main_loop_counter
      00000021H.4 PUBLIC    BIT      BIT       need_led_flash
      02000014H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      02000012H   PUBLIC    XDATA    WORD      timer_1ms_count
      00000021H.3 PUBLIC    BIT      BIT       use_precise_timer
      02000010H   PUBLIC    XDATA    WORD      speedup_cnt
      0200000EH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      0200000CH   PUBLIC    XDATA    WORD      key1_press_time
      0200000BH   PUBLIC    XDATA    BYTE      key_scan_divider
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000007H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000005H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010011BEH   PUBLIC    CODE     ---       Key_Interrupt_Process
      01001687H   PUBLIC    CODE     ---       LED_Control
      01002050H   PUBLIC    CODE     ---       Restore_dly
      01001FA9H   PUBLIC    CODE     ---       _Store_dly
      01001888H   PUBLIC    CODE     ---       Battery_Check
      01001E4FH   PUBLIC    CODE     ---       _Key_Function_Switch_System
      010019EDH   PUBLIC    CODE     ---       _Motor_Step_Control
      01002124H   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 93


      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 94


      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 95


      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000025H.6 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000033H   SYMBOL    XDATA    INT       Key_Input
      02000035H   SYMBOL    XDATA    INT       Charge_Input
      02000037H   SYMBOL    XDATA    INT       Key_State
      02000039H   SYMBOL    XDATA    INT       Key_State_Save
      0200003BH   SYMBOL    XDATA    INT       Charge_State_Save
      0200003DH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000025H.7 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000026H.0 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200003FH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      02000041H   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000026H.1 SYMBOL    BIT      BIT       Voltage_Low
      02000042H   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #144
      010000B6H   LINE      CODE     ---       #145
      010000B6H   LINE      CODE     ---       #151
      010000B8H   LINE      CODE     ---       #154
      010000BAH   LINE      CODE     ---       #155
      010000C1H   LINE      CODE     ---       #158
      010000C4H   LINE      CODE     ---       #159
      010000CBH   LINE      CODE     ---       #161
      010000CEH   LINE      CODE     ---       #163
      010000D1H   LINE      CODE     ---       #164
      010000D4H   LINE      CODE     ---       #166
      010000D7H   LINE      CODE     ---       #167
      010000DAH   LINE      CODE     ---       #171
      010000DCH   LINE      CODE     ---       #172
      010000E3H   LINE      CODE     ---       #173
      010000E7H   LINE      CODE     ---       #174
      010000E9H   LINE      CODE     ---       #175
      010000EFH   LINE      CODE     ---       #176
      010000F5H   LINE      CODE     ---       #177
      010000FBH   LINE      CODE     ---       #178
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 96


      010000FDH   LINE      CODE     ---       #179
      01000108H   LINE      CODE     ---       #180
      0100011CH   LINE      CODE     ---       #181
      0100011CH   LINE      CODE     ---       #182
      01000125H   LINE      CODE     ---       #183
      0100012BH   LINE      CODE     ---       #184
      0100012BH   LINE      CODE     ---       #185
      0100012DH   LINE      CODE     ---       #188
      01000130H   LINE      CODE     ---       #190
      01000136H   LINE      CODE     ---       #191
      01000136H   LINE      CODE     ---       #192
      01000139H   LINE      CODE     ---       #193
      0100013BH   LINE      CODE     ---       #195
      0100013EH   LINE      CODE     ---       #196
      01000149H   LINE      CODE     ---       #199
      0100014FH   LINE      CODE     ---       #200
      0100014FH   LINE      CODE     ---       #201
      0100015DH   LINE      CODE     ---       #202
      0100016FH   LINE      CODE     ---       #203
      0100016FH   LINE      CODE     ---       #204
      01000173H   LINE      CODE     ---       #205
      01000175H   LINE      CODE     ---       #206
      0100017BH   LINE      CODE     ---       #207
      0100017DH   LINE      CODE     ---       #208
      0100017FH   LINE      CODE     ---       #209
      01000181H   LINE      CODE     ---       #210
      01000188H   LINE      CODE     ---       #211
      0100018EH   LINE      CODE     ---       #212
      01000190H   LINE      CODE     ---       #213
      01000192H   LINE      CODE     ---       #214
      0100019AH   LINE      CODE     ---       #215
      0100019CH   LINE      CODE     ---       #216
      0100019EH   LINE      CODE     ---       #217
      010001A0H   LINE      CODE     ---       #218
      010001A0H   LINE      CODE     ---       #219
      010001A3H   LINE      CODE     ---       #221
      010001A3H   LINE      CODE     ---       #222
      010001AAH   LINE      CODE     ---       #224
      010001C6H   LINE      CODE     ---       #225
      010001C6H   LINE      CODE     ---       #226
      010001CCH   LINE      CODE     ---       #227
      010001CCH   LINE      CODE     ---       #228
      010001DAH   LINE      CODE     ---       #229
      010001EBH   LINE      CODE     ---       #230
      010001EBH   LINE      CODE     ---       #231
      010001EFH   LINE      CODE     ---       #232
      010001F1H   LINE      CODE     ---       #233
      010001F3H   LINE      CODE     ---       #234
      010001F8H   LINE      CODE     ---       #235
      010001F8H   LINE      CODE     ---       #236
      010001FAH   LINE      CODE     ---       #237
      01000205H   LINE      CODE     ---       #238
      01000205H   LINE      CODE     ---       #239
      01000213H   LINE      CODE     ---       #240
      01000224H   LINE      CODE     ---       #241
      01000224H   LINE      CODE     ---       #242
      01000228H   LINE      CODE     ---       #243
      0100022AH   LINE      CODE     ---       #244
      0100022EH   LINE      CODE     ---       #245
      01000230H   LINE      CODE     ---       #246
      01000236H   LINE      CODE     ---       #247
      01000236H   LINE      CODE     ---       #248
      01000238H   LINE      CODE     ---       #249
      01000242H   LINE      CODE     ---       #250
      01000242H   LINE      CODE     ---       #251
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 97


      01000248H   LINE      CODE     ---       #252
      0100024AH   LINE      CODE     ---       #253
      0100024EH   LINE      CODE     ---       #254
      01000254H   LINE      CODE     ---       #255
      01000256H   LINE      CODE     ---       #256
      01000256H   LINE      CODE     ---       #257
      01000258H   LINE      CODE     ---       #259
      01000262H   LINE      CODE     ---       #260
      01000262H   LINE      CODE     ---       #261
      01000267H   LINE      CODE     ---       #262
      01000267H   LINE      CODE     ---       #263
      01000267H   LINE      CODE     ---       #265
      01000276H   LINE      CODE     ---       #266
      0100028EH   LINE      CODE     ---       #267
      01000291H   LINE      CODE     ---       #269
      01000293H   LINE      CODE     ---       #270
      01000299H   LINE      CODE     ---       #271
      010002A1H   LINE      CODE     ---       #272
      010002A4H   LINE      CODE     ---       #275
      010002ADH   LINE      CODE     ---       #276
      010002ADH   LINE      CODE     ---       #278
      010002B3H   LINE      CODE     ---       #279
      010002B3H   LINE      CODE     ---       #281
      010002B3H   LINE      CODE     ---       #283
      010002B3H   LINE      CODE     ---       #285
      010002B3H   LINE      CODE     ---       #286
      010002B3H   LINE      CODE     ---       #287
      010002B6H   LINE      CODE     ---       #288
      010002B8H   LINE      CODE     ---       #290
      010002BEH   LINE      CODE     ---       #293
      010002CDH   LINE      CODE     ---       #294
      010002CDH   LINE      CODE     ---       #295
      010002CFH   LINE      CODE     ---       #296
      010002D2H   LINE      CODE     ---       #297
      010002D2H   LINE      CODE     ---       #298
      010002DDH   LINE      CODE     ---       #300
      010002EEH   LINE      CODE     ---       #301
      01000306H   LINE      CODE     ---       #304
      01000315H   LINE      CODE     ---       #305
      01000315H   LINE      CODE     ---       #306
      01000317H   LINE      CODE     ---       #307
      0100031AH   LINE      CODE     ---       #308
      0100031AH   LINE      CODE     ---       #311
      0100031DH   LINE      CODE     ---       #314
      0100032AH   LINE      CODE     ---       #315
      0100032AH   LINE      CODE     ---       #317
      0100032DH   LINE      CODE     ---       #318
      0100032DH   LINE      CODE     ---       #320
      01000330H   LINE      CODE     ---       #321
      01000330H   LINE      CODE     ---       #322
      01000338H   LINE      CODE     ---       #323
      0100033AH   LINE      CODE     ---       #324
      0100033AH   LINE      CODE     ---       #325
      0100033AH   LINE      CODE     ---       #326
      0100033CH   LINE      CODE     ---       #327
      0100033EH   LINE      CODE     ---       #328
      01000344H   LINE      CODE     ---       #329
      01000344H   LINE      CODE     ---       #331
      01000347H   LINE      CODE     ---       #332
      01000347H   LINE      CODE     ---       #334
      0100034AH   LINE      CODE     ---       #335
      0100034AH   LINE      CODE     ---       #336
      01000352H   LINE      CODE     ---       #337
      01000354H   LINE      CODE     ---       #338
      01000354H   LINE      CODE     ---       #339
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 98


      01000354H   LINE      CODE     ---       #340
      01000356H   LINE      CODE     ---       #341
      01000358H   LINE      CODE     ---       #343
      01000358H   LINE      CODE     ---       #345
      0100035EH   LINE      CODE     ---       #346
      0100035EH   LINE      CODE     ---       #348
      01000366H   LINE      CODE     ---       #349
      01000368H   LINE      CODE     ---       #353
      01000368H   LINE      CODE     ---       #354
      0100036AH   LINE      CODE     ---       #355
      0100036AH   LINE      CODE     ---       #358
      01000375H   LINE      CODE     ---       #359
      01000375H   LINE      CODE     ---       #361
      01000378H   LINE      CODE     ---       #362
      01000378H   LINE      CODE     ---       #364
      0100037BH   LINE      CODE     ---       #365
      0100037BH   LINE      CODE     ---       #366
      0100037DH   LINE      CODE     ---       #367
      0100037DH   LINE      CODE     ---       #369
      01000380H   LINE      CODE     ---       #370
      01000380H   LINE      CODE     ---       #371
      01000382H   LINE      CODE     ---       #372
      01000382H   LINE      CODE     ---       #375
      0100038BH   LINE      CODE     ---       #376
      0100038BH   LINE      CODE     ---       #377
      0100038DH   LINE      CODE     ---       #378
      0100038FH   LINE      CODE     ---       #379
      01000391H   LINE      CODE     ---       #380
      01000399H   LINE      CODE     ---       #381
      0100039BH   LINE      CODE     ---       #382
      0100039BH   LINE      CODE     ---       #383
      0100039DH   LINE      CODE     ---       #385
      0100039DH   LINE      CODE     ---       #388
      010003A9H   LINE      CODE     ---       #389
      010003A9H   LINE      CODE     ---       #390
      010003ACH   LINE      CODE     ---       #391
      010003ACH   LINE      CODE     ---       #393
      010003AEH   LINE      CODE     ---       #394
      010003B4H   LINE      CODE     ---       #396
      010003B9H   LINE      CODE     ---       #397
      010003C2H   LINE      CODE     ---       #398
      010003C7H   LINE      CODE     ---       #399
      010003C7H   LINE      CODE     ---       #402
      010003D6H   LINE      CODE     ---       #403
      010003D6H   LINE      CODE     ---       #404
      010003D8H   LINE      CODE     ---       #405
      010003DDH   LINE      CODE     ---       #406
      010003DFH   LINE      CODE     ---       #407
      010003E1H   LINE      CODE     ---       #408
      010003E3H   LINE      CODE     ---       #409
      010003EBH   LINE      CODE     ---       #410
      010003EDH   LINE      CODE     ---       #411
      010003EFH   LINE      CODE     ---       #412
      010003F4H   LINE      CODE     ---       #413
      010003F4H   LINE      CODE     ---       #414
      010003F6H   LINE      CODE     ---       #416
      010003F6H   LINE      CODE     ---       #418
      010003F9H   LINE      CODE     ---       #419
      010003F9H   LINE      CODE     ---       #420
      010003FBH   LINE      CODE     ---       #421
      01000400H   LINE      CODE     ---       #422
      01000407H   LINE      CODE     ---       #423
      01000407H   LINE      CODE     ---       #424
      01000409H   LINE      CODE     ---       #425
      01000409H   LINE      CODE     ---       #426
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 99


      01000409H   LINE      CODE     ---       #429
      0100040FH   LINE      CODE     ---       #430
      0100040FH   LINE      CODE     ---       #431
      0100042CH   LINE      CODE     ---       #432
      0100042CH   LINE      CODE     ---       #433
      0100042FH   LINE      CODE     ---       #434
      0100042FH   LINE      CODE     ---       #436
      0100042FH   LINE      CODE     ---       #437
      0100042FH   LINE      CODE     ---       #438
      0100042FH   LINE      CODE     ---       #439
      0100042FH   LINE      CODE     ---       #440
      0100042FH   LINE      CODE     ---       #441
      0100042FH   LINE      CODE     ---       #442
      01000431H   LINE      CODE     ---       #443
      0100043CH   LINE      CODE     ---       #444
      0100043CH   LINE      CODE     ---       #446
      0100043CH   LINE      CODE     ---       #447
      0100043CH   LINE      CODE     ---       #448
      0100043CH   LINE      CODE     ---       #449
      0100043CH   LINE      CODE     ---       #451
      0100043EH   LINE      CODE     ---       #452
      01000444H   LINE      CODE     ---       #453
      01000444H   LINE      CODE     ---       #455
      01000449H   LINE      CODE     ---       #456
      01000449H   LINE      CODE     ---       #457
      01000449H   LINE      CODE     ---       #458
      01000449H   LINE      CODE     ---       #459
      01000449H   LINE      CODE     ---       #460
      01000449H   LINE      CODE     ---       #461
      01000449H   LINE      CODE     ---       #462
      0100044BH   LINE      CODE     ---       #463
      01000468H   LINE      CODE     ---       #464
      01000468H   LINE      CODE     ---       #465
      0100046BH   LINE      CODE     ---       #466
      0100046BH   LINE      CODE     ---       #468
      01000470H   LINE      CODE     ---       #469
      01000470H   LINE      CODE     ---       #470
      01000470H   LINE      CODE     ---       #471
      01000470H   LINE      CODE     ---       #472
      01000470H   LINE      CODE     ---       #473
      01000470H   LINE      CODE     ---       #474
      01000472H   LINE      CODE     ---       #475
      0100047DH   LINE      CODE     ---       #476
      0100047DH   LINE      CODE     ---       #478
      0100047FH   LINE      CODE     ---       #479
      01000481H   LINE      CODE     ---       #480
      01000489H   LINE      CODE     ---       #481
      0100048BH   LINE      CODE     ---       #482
      0100048DH   LINE      CODE     ---       #483
      01000493H   LINE      CODE     ---       #484
      01000493H   LINE      CODE     ---       #486
      01000499H   LINE      CODE     ---       #487
      010004A2H   LINE      CODE     ---       #488
      010004A4H   LINE      CODE     ---       #489
      010004A6H   LINE      CODE     ---       #490
      010004A8H   LINE      CODE     ---       #491
      010004AFH   LINE      CODE     ---       #492
      010004AFH   LINE      CODE     ---       #493
      010004AFH   LINE      CODE     ---       #494
      010004AFH   LINE      CODE     ---       #497
      010004B5H   LINE      CODE     ---       #498
      010004B5H   LINE      CODE     ---       #499
      010004B7H   LINE      CODE     ---       #500
      010004C5H   LINE      CODE     ---       #502
      010004D6H   LINE      CODE     ---       #503
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 100


      010004D6H   LINE      CODE     ---       #504
      010004DEH   LINE      CODE     ---       #505
      010004E0H   LINE      CODE     ---       #507
      010004E0H   LINE      CODE     ---       #508
      010004E7H   LINE      CODE     ---       #509
      010004E7H   LINE      CODE     ---       #510
      010004E7H   LINE      CODE     ---       #513
      010004EAH   LINE      CODE     ---       #514
      010004EAH   LINE      CODE     ---       #516
      010004F8H   LINE      CODE     ---       #517
      0100050AH   LINE      CODE     ---       #518
      0100050AH   LINE      CODE     ---       #519
      0100050EH   LINE      CODE     ---       #520
      01000510H   LINE      CODE     ---       #521
      01000510H   LINE      CODE     ---       #522
      01000513H   LINE      CODE     ---       #524
      01000513H   LINE      CODE     ---       #526
      01000515H   LINE      CODE     ---       #527
      01000519H   LINE      CODE     ---       #528
      01000519H   LINE      CODE     ---       #529
      0100051CH   LINE      CODE     ---       #532
      0100051CH   LINE      CODE     ---       #533
      0100051FH   LINE      CODE     ---       #535
      0100053FH   LINE      CODE     ---       #536
      0100053FH   LINE      CODE     ---       #537
      01000541H   LINE      CODE     ---       #538
      01000544H   LINE      CODE     ---       #539
      01000561H   LINE      CODE     ---       #540
      01000561H   LINE      CODE     ---       #541
      01000569H   LINE      CODE     ---       #542
      0100056BH   LINE      CODE     ---       #543
      0100057AH   LINE      CODE     ---       #544
      0100057AH   LINE      CODE     ---       #546
      0100057DH   LINE      CODE     ---       #547
      0100057DH   LINE      CODE     ---       #548
      0100057FH   LINE      CODE     ---       #549
      01000585H   LINE      CODE     ---       #550
      0100058AH   LINE      CODE     ---       #551
      01000593H   LINE      CODE     ---       #552
      01000598H   LINE      CODE     ---       #553
      01000598H   LINE      CODE     ---       #555
      010005A7H   LINE      CODE     ---       #556
      010005A7H   LINE      CODE     ---       #557
      010005A9H   LINE      CODE     ---       #558
      010005AEH   LINE      CODE     ---       #559
      010005B7H   LINE      CODE     ---       #560
      010005B9H   LINE      CODE     ---       #561
      010005BBH   LINE      CODE     ---       #562
      010005C0H   LINE      CODE     ---       #563
      010005C0H   LINE      CODE     ---       #564
      010005C0H   LINE      CODE     ---       #565
      010005CFH   LINE      CODE     ---       #566
      010005CFH   LINE      CODE     ---       #568
      010005D1H   LINE      CODE     ---       #569
      010005D6H   LINE      CODE     ---       #570
      010005DDH   LINE      CODE     ---       #571
      010005DDH   LINE      CODE     ---       #572
      010005DDH   LINE      CODE     ---       #574
      010005ECH   LINE      CODE     ---       #577
      010005F7H   LINE      CODE     ---       #578
      010005F7H   LINE      CODE     ---       #579
      010005FDH   LINE      CODE     ---       #580
      010005FDH   LINE      CODE     ---       #581
      0100060AH   LINE      CODE     ---       #582
      0100060AH   LINE      CODE     ---       #583
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 101


      0100060CH   LINE      CODE     ---       #584
      0100060EH   LINE      CODE     ---       #585
      01000616H   LINE      CODE     ---       #586
      01000618H   LINE      CODE     ---       #588
      01000618H   LINE      CODE     ---       #589
      0100061EH   LINE      CODE     ---       #590
      0100061EH   LINE      CODE     ---       #592
      01000626H   LINE      CODE     ---       #593
      01000626H   LINE      CODE     ---       #594
      01000627H   LINE      CODE     ---       #595
      0100062BH   LINE      CODE     ---       #596
      0100062EH   LINE      CODE     ---       #597
      01000637H   LINE      CODE     ---       #598
      01000637H   LINE      CODE     ---       #599
      0100063CH   LINE      CODE     ---       #601
      01000685H   LINE      CODE     ---       #602
      01000685H   LINE      CODE     ---       #603
      01000685H   LINE      CODE     ---       #604
      01000685H   LINE      CODE     ---       #605
      01000693H   LINE      CODE     ---       #606
      010006A0H   LINE      CODE     ---       #607
      010006A0H   LINE      CODE     ---       #608
      010006A7H   LINE      CODE     ---       #609
      010006A9H   LINE      CODE     ---       #611
      010006A9H   LINE      CODE     ---       #612
      010006ABH   LINE      CODE     ---       #613
      010006B2H   LINE      CODE     ---       #614
      010006B2H   LINE      CODE     ---       #615
      010006B2H   LINE      CODE     ---       #616
      010006B2H   LINE      CODE     ---       #617
      010006B2H   LINE      CODE     ---       #618
      010006B4H   LINE      CODE     ---       #619
      010006B4H   LINE      CODE     ---       #620
      010006BDH   LINE      CODE     ---       #621
      010006C8H   LINE      CODE     ---       #622
      010006C8H   LINE      CODE     ---       #623
      010006CAH   LINE      CODE     ---       #624
      010006CAH   LINE      CODE     ---       #625
      010006CAH   LINE      CODE     ---       #626
      010006CAH   LINE      CODE     ---       #627
      010006CAH   LINE      CODE     ---       #628
      010006CAH   LINE      CODE     ---       #629
      010006CDH   LINE      CODE     ---       #630
      010006CDH   LINE      CODE     ---       #631
      010006CDH   LINE      CODE     ---       #632
      010006DBH   LINE      CODE     ---       #633
      010006E8H   LINE      CODE     ---       #634
      010006E8H   LINE      CODE     ---       #635
      010006EEH   LINE      CODE     ---       #636
      010006F0H   LINE      CODE     ---       #638
      010006F0H   LINE      CODE     ---       #639
      010006F2H   LINE      CODE     ---       #640
      010006F9H   LINE      CODE     ---       #641
      010006F9H   LINE      CODE     ---       #642
      01000704H   LINE      CODE     ---       #643
      01000709H   LINE      CODE     ---       #644
      01000709H   LINE      CODE     ---       #645
      0100070CH   LINE      CODE     ---       #646
      0100070CH   LINE      CODE     ---       #647
      01000715H   LINE      CODE     ---       #648
      0100071DH   LINE      CODE     ---       #649
      0100071DH   LINE      CODE     ---       #650
      0100071FH   LINE      CODE     ---       #651
      0100071FH   LINE      CODE     ---       #652
      0100071FH   LINE      CODE     ---       #653
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 102


      0100071FH   LINE      CODE     ---       #654
      0100071FH   LINE      CODE     ---       #655
      0100071FH   LINE      CODE     ---       #656
      01000721H   LINE      CODE     ---       #657
      01000721H   LINE      CODE     ---       #658
      0100072AH   LINE      CODE     ---       #659
      01000732H   LINE      CODE     ---       #660
      01000732H   LINE      CODE     ---       #661
      01000734H   LINE      CODE     ---       #662
      01000734H   LINE      CODE     ---       #663
      01000739H   LINE      CODE     ---       #664
      01000739H   LINE      CODE     ---       #665
      01000739H   LINE      CODE     ---       #666
      01000739H   LINE      CODE     ---       #667
      0100073BH   LINE      CODE     ---       #668
      0100073BH   LINE      CODE     ---       #669
      01000744H   LINE      CODE     ---       #670
      0100074CH   LINE      CODE     ---       #671
      0100074CH   LINE      CODE     ---       #672
      0100074EH   LINE      CODE     ---       #673
      0100074EH   LINE      CODE     ---       #674
      01000754H   LINE      CODE     ---       #675
      01000756H   LINE      CODE     ---       #676
      01000756H   LINE      CODE     ---       #677
      01000756H   LINE      CODE     ---       #678
      01000759H   LINE      CODE     ---       #679
      01000759H   LINE      CODE     ---       #680
      0100075FH   LINE      CODE     ---       #681
      01000761H   LINE      CODE     ---       #682
      01000769H   LINE      CODE     ---       #683
      0100076BH   LINE      CODE     ---       #684
      0100076EH   LINE      CODE     ---       #685
      0100076EH   LINE      CODE     ---       #686
      0100076EH   LINE      CODE     ---       #687
      01000777H   LINE      CODE     ---       #688
      0100077FH   LINE      CODE     ---       #689
      0100077FH   LINE      CODE     ---       #690
      01000781H   LINE      CODE     ---       #691
      01000781H   LINE      CODE     ---       #692
      01000787H   LINE      CODE     ---       #693
      01000789H   LINE      CODE     ---       #694
      01000789H   LINE      CODE     ---       #695
      01000789H   LINE      CODE     ---       #696
      01000789H   LINE      CODE     ---       #697
      0100078BH   LINE      CODE     ---       #698
      0100078BH   LINE      CODE     ---       #699
      0100078BH   LINE      CODE     ---       #700
      01000794H   LINE      CODE     ---       #701
      0100079CH   LINE      CODE     ---       #702
      0100079CH   LINE      CODE     ---       #703
      0100079EH   LINE      CODE     ---       #704
      0100079EH   LINE      CODE     ---       #705
      010007A4H   LINE      CODE     ---       #706
      010007A6H   LINE      CODE     ---       #707
      010007A6H   LINE      CODE     ---       #708
      010007A6H   LINE      CODE     ---       #709
      010007A6H   LINE      CODE     ---       #710
      010007A8H   LINE      CODE     ---       #711
      010007A8H   LINE      CODE     ---       #712
      010007A8H   LINE      CODE     ---       #713
      010007B1H   LINE      CODE     ---       #714
      010007B9H   LINE      CODE     ---       #715
      010007B9H   LINE      CODE     ---       #716
      010007BBH   LINE      CODE     ---       #717
      010007BBH   LINE      CODE     ---       #718
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 103


      010007C1H   LINE      CODE     ---       #719
      010007C3H   LINE      CODE     ---       #720
      010007C3H   LINE      CODE     ---       #721
      010007C3H   LINE      CODE     ---       #722
      010007C3H   LINE      CODE     ---       #723
      010007C5H   LINE      CODE     ---       #724
      010007C5H   LINE      CODE     ---       #725
      010007C5H   LINE      CODE     ---       #726
      010007CEH   LINE      CODE     ---       #727
      010007D6H   LINE      CODE     ---       #728
      010007D6H   LINE      CODE     ---       #729
      010007D8H   LINE      CODE     ---       #730
      010007D8H   LINE      CODE     ---       #731
      010007DEH   LINE      CODE     ---       #732
      010007E0H   LINE      CODE     ---       #733
      010007E2H   LINE      CODE     ---       #734
      010007E8H   LINE      CODE     ---       #735
      010007E8H   LINE      CODE     ---       #736
      010007EAH   LINE      CODE     ---       #737
      010007EAH   LINE      CODE     ---       #738
      010007EAH   LINE      CODE     ---       #739
      010007F0H   LINE      CODE     ---       #740
      010007F2H   LINE      CODE     ---       #741
      010007F2H   LINE      CODE     ---       #742
      010007F2H   LINE      CODE     ---       #743
      010007F2H   LINE      CODE     ---       #744
      010007F2H   LINE      CODE     ---       #745
      010007F2H   LINE      CODE     ---       #746
      010007F2H   LINE      CODE     ---       #747
      010007F2H   LINE      CODE     ---       #748
      010007F2H   LINE      CODE     ---       #751
      01000803H   LINE      CODE     ---       #752
      01000803H   LINE      CODE     ---       #753
      01000805H   LINE      CODE     ---       #754
      01000807H   LINE      CODE     ---       #755
      01000815H   LINE      CODE     ---       #757
      01000821H   LINE      CODE     ---       #758
      01000821H   LINE      CODE     ---       #759
      01000823H   LINE      CODE     ---       #760
      01000825H   LINE      CODE     ---       #761
      01000827H   LINE      CODE     ---       #762
      01000827H   LINE      CODE     ---       #763
      0100082DH   LINE      CODE     ---       #764
      0100082DH   LINE      CODE     ---       #765
      01000830H   LINE      CODE     ---       #766
      01000837H   LINE      CODE     ---       #767
      01000839H   LINE      CODE     ---       #768
      01000839H   LINE      CODE     ---       #769
      0100083CH   LINE      CODE     ---       #770
      0100083CH   LINE      CODE     ---       #771
      01000844H   LINE      CODE     ---       #772
      01000844H   LINE      CODE     ---       #773
      01000847H   LINE      CODE     ---       #774
      01000847H   LINE      CODE     ---       #776
      0100084AH   LINE      CODE     ---       #777
      0100084AH   LINE      CODE     ---       #778
      0100084EH   LINE      CODE     ---       #779
      01000850H   LINE      CODE     ---       #781
      01000850H   LINE      CODE     ---       #783
      0100085DH   LINE      CODE     ---       #784
      0100085DH   LINE      CODE     ---       #785
      01000865H   LINE      CODE     ---       #786
      01000867H   LINE      CODE     ---       #787
      0100086DH   LINE      CODE     ---       #788
      0100086DH   LINE      CODE     ---       #789
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 104


      01000875H   LINE      CODE     ---       #790
      01000877H   LINE      CODE     ---       #791
      0100087DH   LINE      CODE     ---       #792
      0100087DH   LINE      CODE     ---       #793
      01000885H   LINE      CODE     ---       #794
      01000885H   LINE      CODE     ---       #795
      01000885H   LINE      CODE     ---       #796
      01000885H   LINE      CODE     ---       #797
      01000888H   LINE      CODE     ---       #798
      01000888H   LINE      CODE     ---       #799
      0100088EH   LINE      CODE     ---       #800
      0100088EH   LINE      CODE     ---       #801
      0100089FH   LINE      CODE     ---       #802
      0100089FH   LINE      CODE     ---       #803
      0100089FH   LINE      CODE     ---       #804
      010008A1H   LINE      CODE     ---       #806
      010008A1H   LINE      CODE     ---       #807
      010008A9H   LINE      CODE     ---       #808
      010008B0H   LINE      CODE     ---       #809
      010008B6H   LINE      CODE     ---       #810
      010008B8H   LINE      CODE     ---       #811
      010008BAH   LINE      CODE     ---       #812
      010008BCH   LINE      CODE     ---       #813
      010008BEH   LINE      CODE     ---       #814
      010008C0H   LINE      CODE     ---       #815
      010008C0H   LINE      CODE     ---       #816
      010008C3H   LINE      CODE     ---       #818
      010008C3H   LINE      CODE     ---       #819
      010008CBH   LINE      CODE     ---       #820
      010008D2H   LINE      CODE     ---       #821
      010008D8H   LINE      CODE     ---       #822
      010008DAH   LINE      CODE     ---       #823
      010008DCH   LINE      CODE     ---       #824
      010008DEH   LINE      CODE     ---       #825
      010008E0H   LINE      CODE     ---       #826
      010008E0H   LINE      CODE     ---       #827
      010008E3H   LINE      CODE     ---       #828
      010008F4H   LINE      CODE     ---       #829
      010008F4H   LINE      CODE     ---       #830
      010008FCH   LINE      CODE     ---       #831
      010008FFH   LINE      CODE     ---       #833
      010008FFH   LINE      CODE     ---       #834
      01000905H   LINE      CODE     ---       #835
      01000905H   LINE      CODE     ---       #836
      01000905H   LINE      CODE     ---       #837
      01000905H   LINE      CODE     ---       #838
      01000905H   LINE      CODE     ---       #839
      01000905H   LINE      CODE     ---       #840
      01000905H   LINE      CODE     ---       #841
      01000905H   LINE      CODE     ---       #842
      01000907H   LINE      CODE     ---       #844
      01000907H   LINE      CODE     ---       #845
      0100090DH   LINE      CODE     ---       #846
      0100090DH   LINE      CODE     ---       #847
      01000914H   LINE      CODE     ---       #848
      01000917H   LINE      CODE     ---       #850
      01000917H   LINE      CODE     ---       #851
      0100091FH   LINE      CODE     ---       #852
      01000926H   LINE      CODE     ---       #853
      0100092CH   LINE      CODE     ---       #854
      0100092EH   LINE      CODE     ---       #855
      01000930H   LINE      CODE     ---       #856
      01000932H   LINE      CODE     ---       #857
      01000932H   LINE      CODE     ---       #858
      01000932H   LINE      CODE     ---       #859
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 105


      01000932H   LINE      CODE     ---       #860
      01000935H   LINE      CODE     ---       #861
      01000940H   LINE      CODE     ---       #862
      01000940H   LINE      CODE     ---       #863
      01000942H   LINE      CODE     ---       #864
      01000944H   LINE      CODE     ---       #865
      01000946H   LINE      CODE     ---       #866
      01000948H   LINE      CODE     ---       #867
      0100094AH   LINE      CODE     ---       #868
      0100094DH   LINE      CODE     ---       #869
      01000956H   LINE      CODE     ---       #870
      01000956H   LINE      CODE     ---       #871
      01000956H   LINE      CODE     ---       #872
      0100095DH   LINE      CODE     ---       #873
      0100095DH   LINE      CODE     ---       #874
      0100095FH   LINE      CODE     ---       #875
      01000961H   LINE      CODE     ---       #876
      01000963H   LINE      CODE     ---       #877
      01000965H   LINE      CODE     ---       #878
      01000967H   LINE      CODE     ---       #879
      01000969H   LINE      CODE     ---       #880
      0100096BH   LINE      CODE     ---       #883
      0100096DH   LINE      CODE     ---       #884
      0100096FH   LINE      CODE     ---       #885
      01000971H   LINE      CODE     ---       #886
      01000977H   LINE      CODE     ---       #887
      0100097DH   LINE      CODE     ---       #888
      0100097FH   LINE      CODE     ---       #889
      01000981H   LINE      CODE     ---       #890
      01000983H   LINE      CODE     ---       #891
      01000985H   LINE      CODE     ---       #893
      01000988H   LINE      CODE     ---       #895
      0100098BH   LINE      CODE     ---       #897
      0100098EH   LINE      CODE     ---       #899
      01000991H   LINE      CODE     ---       #900
      01000994H   LINE      CODE     ---       #901
      01000997H   LINE      CODE     ---       #902
      0100099AH   LINE      CODE     ---       #903
      0100099DH   LINE      CODE     ---       #904
      010009A0H   LINE      CODE     ---       #905
      010009A3H   LINE      CODE     ---       #907
      010009A9H   LINE      CODE     ---       #908
      010009A9H   LINE      CODE     ---       #909
      ---         BLOCKEND  ---      ---       LVL=0

      01002124H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      01002124H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      01002124H   LINE      CODE     ---       #913
      01002124H   LINE      CODE     ---       #914
      01002124H   LINE      CODE     ---       #916
      0100212EH   LINE      CODE     ---       #917
      01002143H   LINE      CODE     ---       #918
      ---         BLOCKEND  ---      ---       LVL=0

      010019EDH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      010019EDH   BLOCK     CODE     NEAR LAB  LVL=1
      02000000H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      010019EDH   LINE      CODE     ---       #920
      010019EDH   LINE      CODE     ---       #921
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 106


      010019EDH   LINE      CODE     ---       #924
      010019F2H   LINE      CODE     ---       #925
      010019F2H   LINE      CODE     ---       #926
      010019F8H   LINE      CODE     ---       #927
      01001A02H   LINE      CODE     ---       #928
      01001A02H   LINE      CODE     ---       #929
      01001A04H   LINE      CODE     ---       #930
      01001A04H   LINE      CODE     ---       #931
      01001A06H   LINE      CODE     ---       #933
      01001A06H   LINE      CODE     ---       #934
      01001A13H   LINE      CODE     ---       #935
      01001A13H   LINE      CODE     ---       #936
      01001A16H   LINE      CODE     ---       #937
      01001A16H   LINE      CODE     ---       #938
      01001A1CH   LINE      CODE     ---       #939
      01001A1CH   LINE      CODE     ---       #941
      01001A44H   LINE      CODE     ---       #942
      01001A44H   LINE      CODE     ---       #943
      01001A48H   LINE      CODE     ---       #944
      01001A4CH   LINE      CODE     ---       #945
      01001A52H   LINE      CODE     ---       #946
      01001A58H   LINE      CODE     ---       #947
      01001A61H   LINE      CODE     ---       #948
      01001A6AH   LINE      CODE     ---       #949
      01001A6EH   LINE      CODE     ---       #950
      01001A77H   LINE      CODE     ---       #951
      01001A77H   LINE      CODE     ---       #952
      01001A7FH   LINE      CODE     ---       #954
      01001A7FH   LINE      CODE     ---       #955
      01001A7FH   LINE      CODE     ---       #956
      ---         BLOCKEND  ---      ---       LVL=0

      01001E4FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      01001E4FH   LINE      CODE     ---       #958
      01001E4FH   LINE      CODE     ---       #959
      01001E4FH   LINE      CODE     ---       #960
      01001E5FH   LINE      CODE     ---       #961
      01001E5FH   LINE      CODE     ---       #962
      01001E5FH   LINE      CODE     ---       #963
      01001E68H   LINE      CODE     ---       #964
      01001E6EH   LINE      CODE     ---       #965
      01001E6FH   LINE      CODE     ---       #966
      01001E6FH   LINE      CODE     ---       #967
      01001E72H   LINE      CODE     ---       #968
      01001E72H   LINE      CODE     ---       #969
      01001E77H   LINE      CODE     ---       #970
      01001E78H   LINE      CODE     ---       #972
      01001E78H   LINE      CODE     ---       #973
      01001E7EH   LINE      CODE     ---       #974
      01001E7EH   LINE      CODE     ---       #975
      01001E7FH   LINE      CODE     ---       #976
      01001E7FH   LINE      CODE     ---       #977
      01001E88H   LINE      CODE     ---       #978
      01001E8EH   LINE      CODE     ---       #979
      01001E8FH   LINE      CODE     ---       #980
      01001E8FH   LINE      CODE     ---       #981
      01001E95H   LINE      CODE     ---       #982
      01001E95H   LINE      CODE     ---       #983
      01001E95H   LINE      CODE     ---       #984
      01001E95H   LINE      CODE     ---       #985
      01001E95H   LINE      CODE     ---       #986
      ---         BLOCKEND  ---      ---       LVL=0

      01001888H   BLOCK     CODE     ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 107


      01001888H   BLOCK     CODE     NEAR LAB  LVL=1
      02000001H   SYMBOL    XDATA    BYTE      adc_state
      02000002H   SYMBOL    XDATA    BYTE      sample_count
      02000003H   SYMBOL    XDATA    WORD      adc_sum
      ---         BLOCKEND  ---      ---       LVL=1
      01001888H   LINE      CODE     ---       #989
      01001888H   LINE      CODE     ---       #990
      01001888H   LINE      CODE     ---       #996
      01001899H   LINE      CODE     ---       #997
      01001899H   LINE      CODE     ---       #998
      01001899H   LINE      CODE     ---       #999
      0100189EH   LINE      CODE     ---       #1000
      010018A4H   LINE      CODE     ---       #1001
      010018A5H   LINE      CODE     ---       #1003
      010018A5H   LINE      CODE     ---       #1004
      010018AEH   LINE      CODE     ---       #1005
      010018AEH   LINE      CODE     ---       #1006
      010018BDH   LINE      CODE     ---       #1007
      010018C3H   LINE      CODE     ---       #1008
      010018C6H   LINE      CODE     ---       #1010
      010018D3H   LINE      CODE     ---       #1011
      010018D3H   LINE      CODE     ---       #1012
      010018EAH   LINE      CODE     ---       #1013
      010018F1H   LINE      CODE     ---       #1014
      010018F5H   LINE      CODE     ---       #1015
      010018FBH   LINE      CODE     ---       #1016
      010018FCH   LINE      CODE     ---       #1018
      010018FCH   LINE      CODE     ---       #1019
      010018FCH   LINE      CODE     ---       #1020
      010018FCH   LINE      CODE     ---       #1021
      010018FCH   LINE      CODE     ---       #1022
      010018FEH   LINE      CODE     ---       #1024
      010018FEH   LINE      CODE     ---       #1025
      0100190EH   LINE      CODE     ---       #1026
      0100190EH   LINE      CODE     ---       #1027
      01001914H   LINE      CODE     ---       #1028
      0100191FH   LINE      CODE     ---       #1029
      01001921H   LINE      CODE     ---       #1031
      01001921H   LINE      CODE     ---       #1032
      01001923H   LINE      CODE     ---       #1033
      01001928H   LINE      CODE     ---       #1034
      01001928H   LINE      CODE     ---       #1036
      01001931H   LINE      CODE     ---       #1037
      01001931H   LINE      CODE     ---       #1038
      01001937H   LINE      CODE     ---       #1039
      01001942H   LINE      CODE     ---       #1040
      01001944H   LINE      CODE     ---       #1042
      01001944H   LINE      CODE     ---       #1043
      01001949H   LINE      CODE     ---       #1044
      0100194BH   LINE      CODE     ---       #1045
      0100194BH   LINE      CODE     ---       #1046
      0100194BH   LINE      CODE     ---       #1047
      0100194DH   LINE      CODE     ---       #1049
      0100194DH   LINE      CODE     ---       #1050
      01001952H   LINE      CODE     ---       #1051
      01001952H   LINE      CODE     ---       #1052
      01001952H   LINE      CODE     ---       #1053
      ---         BLOCKEND  ---      ---       LVL=0

      01001FA9H   BLOCK     CODE     ---       LVL=0
      02000044H   SYMBOL    XDATA    WORD      dly1
      01001FA9H   LINE      CODE     ---       #1055
      01001FB1H   LINE      CODE     ---       #1056
      01001FB1H   LINE      CODE     ---       #1057
      01001FB4H   LINE      CODE     ---       #1058
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 108


      01001FB7H   LINE      CODE     ---       #1059
      01001FC0H   LINE      CODE     ---       #1060
      01001FCFH   LINE      CODE     ---       #1061
      01001FD2H   LINE      CODE     ---       #1062
      01001FD5H   LINE      CODE     ---       #1063
      ---         BLOCKEND  ---      ---       LVL=0

      01002050H   BLOCK     CODE     ---       LVL=0
      01002050H   BLOCK     CODE     NEAR LAB  LVL=1
      02000044H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002050H   LINE      CODE     ---       #1065
      01002050H   LINE      CODE     ---       #1066
      01002050H   LINE      CODE     ---       #1069
      01002053H   LINE      CODE     ---       #1070
      01002056H   LINE      CODE     ---       #1071
      01002067H   LINE      CODE     ---       #1072
      0100206AH   LINE      CODE     ---       #1073
      0100206DH   LINE      CODE     ---       #1075
      01002075H   LINE      CODE     ---       #1076
      ---         BLOCKEND  ---      ---       LVL=0

      01001687H   BLOCK     CODE     ---       LVL=0
      01001687H   LINE      CODE     ---       #1078
      01001687H   LINE      CODE     ---       #1079
      01001687H   LINE      CODE     ---       #1081
      01001692H   LINE      CODE     ---       #1082
      01001692H   LINE      CODE     ---       #1084
      01001695H   LINE      CODE     ---       #1085
      01001695H   LINE      CODE     ---       #1087
      01001697H   LINE      CODE     ---       #1088
      0100169AH   LINE      CODE     ---       #1089
      0100169AH   LINE      CODE     ---       #1091
      010016A1H   LINE      CODE     ---       #1092
      010016A3H   LINE      CODE     ---       #1093
      010016A5H   LINE      CODE     ---       #1095
      010016A5H   LINE      CODE     ---       #1096
      010016A7H   LINE      CODE     ---       #1097
      010016A7H   LINE      CODE     ---       #1098
      010016A9H   LINE      CODE     ---       #1100
      010016A9H   LINE      CODE     ---       #1102
      010016ABH   LINE      CODE     ---       #1103
      010016AEH   LINE      CODE     ---       #1104
      010016AEH   LINE      CODE     ---       #1106
      010016B1H   LINE      CODE     ---       #1107
      010016B1H   LINE      CODE     ---       #1108
      010016B3H   LINE      CODE     ---       #1109
      010016B5H   LINE      CODE     ---       #1111
      010016B5H   LINE      CODE     ---       #1112
      010016B7H   LINE      CODE     ---       #1113
      010016B7H   LINE      CODE     ---       #1114
      010016B9H   LINE      CODE     ---       #1116
      010016B9H   LINE      CODE     ---       #1118
      010016BBH   LINE      CODE     ---       #1119
      010016BBH   LINE      CODE     ---       #1120
      010016BBH   LINE      CODE     ---       #1121
      010016BDH   LINE      CODE     ---       #1123
      010016BDH   LINE      CODE     ---       #1125
      010016C0H   LINE      CODE     ---       #1126
      010016C0H   LINE      CODE     ---       #1127
      010016C2H   LINE      CODE     ---       #1128
      010016C5H   LINE      CODE     ---       #1129
      010016C5H   LINE      CODE     ---       #1131
      010016CCH   LINE      CODE     ---       #1132
      010016CEH   LINE      CODE     ---       #1133
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 109


      010016D0H   LINE      CODE     ---       #1135
      010016D0H   LINE      CODE     ---       #1136
      010016D2H   LINE      CODE     ---       #1137
      010016D2H   LINE      CODE     ---       #1138
      010016D4H   LINE      CODE     ---       #1140
      010016D4H   LINE      CODE     ---       #1141
      010016D7H   LINE      CODE     ---       #1142
      010016D7H   LINE      CODE     ---       #1143
      010016D9H   LINE      CODE     ---       #1144
      010016E3H   LINE      CODE     ---       #1146
      010016E3H   LINE      CODE     ---       #1147
      010016EAH   LINE      CODE     ---       #1148
      010016ECH   LINE      CODE     ---       #1149
      010016ECH   LINE      CODE     ---       #1150
      010016EEH   LINE      CODE     ---       #1152
      010016EEH   LINE      CODE     ---       #1153
      010016F0H   LINE      CODE     ---       #1154
      010016FAH   LINE      CODE     ---       #1156
      010016FAH   LINE      CODE     ---       #1157
      01001701H   LINE      CODE     ---       #1158
      01001703H   LINE      CODE     ---       #1159
      01001703H   LINE      CODE     ---       #1160
      01001703H   LINE      CODE     ---       #1161
      01001703H   LINE      CODE     ---       #1162
      01001703H   LINE      CODE     ---       #1165
      01001706H   LINE      CODE     ---       #1166
      01001706H   LINE      CODE     ---       #1167
      01001714H   LINE      CODE     ---       #1168
      01001723H   LINE      CODE     ---       #1169
      01001723H   LINE      CODE     ---       #1170
      01001725H   LINE      CODE     ---       #1171
      01001727H   LINE      CODE     ---       #1173
      01001727H   LINE      CODE     ---       #1174
      01001729H   LINE      CODE     ---       #1175
      0100172BH   LINE      CODE     ---       #1176
      01001732H   LINE      CODE     ---       #1177
      01001732H   LINE      CODE     ---       #1178
      01001732H   LINE      CODE     ---       #1181
      01001738H   LINE      CODE     ---       #1182
      01001742H   LINE      CODE     ---       #1183
      01001742H   LINE      CODE     ---       #1184
      01001744H   LINE      CODE     ---       #1185
      01001749H   LINE      CODE     ---       #1186
      01001749H   LINE      CODE     ---       #1189
      01001757H   LINE      CODE     ---       #1190
      01001766H   LINE      CODE     ---       #1191
      01001766H   LINE      CODE     ---       #1192
      0100176AH   LINE      CODE     ---       #1193
      0100176FH   LINE      CODE     ---       #1194
      0100176FH   LINE      CODE     ---       #1197
      01001777H   LINE      CODE     ---       #1198
      01001777H   LINE      CODE     ---       #1199
      0100177DH   LINE      CODE     ---       #1200
      01001787H   LINE      CODE     ---       #1201
      01001787H   LINE      CODE     ---       #1202
      01001789H   LINE      CODE     ---       #1203
      0100178EH   LINE      CODE     ---       #1204
      0100178EH   LINE      CODE     ---       #1205
      0100178FH   LINE      CODE     ---       #1207
      0100178FH   LINE      CODE     ---       #1208
      01001794H   LINE      CODE     ---       #1209
      01001796H   LINE      CODE     ---       #1210
      01001796H   LINE      CODE     ---       #1211
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 110


      010011BEH   BLOCK     CODE     ---       LVL=0
      010011BEH   LINE      CODE     ---       #1220
      010011BEH   LINE      CODE     ---       #1221
      010011BEH   LINE      CODE     ---       #1223
      010011CAH   LINE      CODE     ---       #1224
      010011CAH   LINE      CODE     ---       #1225
      010011CDH   LINE      CODE     ---       #1226
      010011CDH   LINE      CODE     ---       #1227
      010011CFH   LINE      CODE     ---       #1228
      010011D1H   LINE      CODE     ---       #1229
      010011D3H   LINE      CODE     ---       #1230
      010011D5H   LINE      CODE     ---       #1231
      010011D7H   LINE      CODE     ---       #1232
      010011DDH   LINE      CODE     ---       #1233
      010011DDH   LINE      CODE     ---       #1234
      010011DDH   LINE      CODE     ---       #1237
      010011FEH   LINE      CODE     ---       #1238
      010011FEH   LINE      CODE     ---       #1240
      01001204H   LINE      CODE     ---       #1241
      01001204H   LINE      CODE     ---       #1242
      0100120DH   LINE      CODE     ---       #1243
      01001213H   LINE      CODE     ---       #1244
      01001215H   LINE      CODE     ---       #1245
      01001217H   LINE      CODE     ---       #1246
      01001219H   LINE      CODE     ---       #1247
      0100121BH   LINE      CODE     ---       #1248
      0100121FH   LINE      CODE     ---       #1249
      0100121FH   LINE      CODE     ---       #1250
      0100121FH   LINE      CODE     ---       #1253
      01001222H   LINE      CODE     ---       #1254
      01001222H   LINE      CODE     ---       #1255
      01001224H   LINE      CODE     ---       #1257
      0100123FH   LINE      CODE     ---       #1258
      0100123FH   LINE      CODE     ---       #1260
      01001241H   LINE      CODE     ---       #1261
      01001243H   LINE      CODE     ---       #1262
      01001245H   LINE      CODE     ---       #1265
      0100124EH   LINE      CODE     ---       #1266
      01001254H   LINE      CODE     ---       #1267
      01001256H   LINE      CODE     ---       #1268
      01001258H   LINE      CODE     ---       #1269
      0100125CH   LINE      CODE     ---       #1270
      01001263H   LINE      CODE     ---       #1271
      01001265H   LINE      CODE     ---       #1272
      01001274H   LINE      CODE     ---       #1273
      01001274H   LINE      CODE     ---       #1275
      0100127AH   LINE      CODE     ---       #1276
      0100127CH   LINE      CODE     ---       #1277
      0100127EH   LINE      CODE     ---       #1278
      01001280H   LINE      CODE     ---       #1279
      01001282H   LINE      CODE     ---       #1280
      01001282H   LINE      CODE     ---       #1281
      01001282H   LINE      CODE     ---       #1284
      010012A3H   LINE      CODE     ---       #1285
      010012A3H   LINE      CODE     ---       #1287
      010012A9H   LINE      CODE     ---       #1288
      010012A9H   LINE      CODE     ---       #1289
      010012B2H   LINE      CODE     ---       #1290
      010012B8H   LINE      CODE     ---       #1291
      010012BAH   LINE      CODE     ---       #1292
      010012BCH   LINE      CODE     ---       #1293
      010012BEH   LINE      CODE     ---       #1294
      010012C0H   LINE      CODE     ---       #1295
      010012C5H   LINE      CODE     ---       #1296
      010012C5H   LINE      CODE     ---       #1297
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 111


      010012C5H   LINE      CODE     ---       #1300
      010012C8H   LINE      CODE     ---       #1301
      010012C8H   LINE      CODE     ---       #1302
      010012CAH   LINE      CODE     ---       #1304
      010012E5H   LINE      CODE     ---       #1305
      010012E5H   LINE      CODE     ---       #1307
      010012E7H   LINE      CODE     ---       #1308
      010012E9H   LINE      CODE     ---       #1309
      010012EBH   LINE      CODE     ---       #1312
      010012F4H   LINE      CODE     ---       #1313
      010012FAH   LINE      CODE     ---       #1314
      010012FCH   LINE      CODE     ---       #1315
      010012FEH   LINE      CODE     ---       #1316
      01001303H   LINE      CODE     ---       #1317
      0100130AH   LINE      CODE     ---       #1318
      0100130BH   LINE      CODE     ---       #1319
      0100131AH   LINE      CODE     ---       #1320
      0100131AH   LINE      CODE     ---       #1322
      01001320H   LINE      CODE     ---       #1323
      01001322H   LINE      CODE     ---       #1324
      01001324H   LINE      CODE     ---       #1325
      01001326H   LINE      CODE     ---       #1326
      01001328H   LINE      CODE     ---       #1327
      01001328H   LINE      CODE     ---       #1328
      01001328H   LINE      CODE     ---       #1329
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      010009ACH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000AB5H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000B5CH   PUBLIC    CODE     ---       ?C?FCASTC
      01000B57H   PUBLIC    CODE     ---       ?C?FCASTI
      01000B52H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000B90H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000BC5H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000BCFH   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000BA7H   PUBLIC    CODE     ---       ?C?FPRESULT
      01000BBBH   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000BCCH   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000BDAH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000C17H   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000D23H   PUBLIC    CODE     ---       ?C?FPADD
      01000D1FH   PUBLIC    CODE     ---       ?C?FPSUB

      ---         MODULE    ---      ---       ?C?FTNPWR
      01000E44H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      010019A8H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/22/2025  16:36:19  PAGE 112


      01000F54H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000F7AH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000F93H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000FC0H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000FD2H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01000FF4H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01001049H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      0100109BH   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      0100112DH   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      0100113BH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      01001147H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      01001178H   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      0100118FH   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      01001198H   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=15.2 xdata=162 const=13 code=8808
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
