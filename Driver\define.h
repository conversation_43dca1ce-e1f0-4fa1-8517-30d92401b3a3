#ifndef __DEFINE_H
#define __DEFINE_H
#include "cms8s6990.h"


#define K1         P17
#define K2         P16
#define K3         P15

#define LEDRON     P23=1
#define LEDROFF    P23=0
#define LEDRTOGGLE P23^=1
#define LEDGON     P22=1
#define LEDGOFF    P22=0
#define LEDGTOGGLE P22^=1

#define CHG_FULL   P25
#define CHG_CHARGE P24


#define BAT_LOW_LEVEL 1902      //3.1
#define BAT_WARNING_LEVEL 2149  //3.5


extern bit MOTOR_RUNNING_FLAG;

#define Battery_ADC ADC_CH_14  


extern bit K1_cnt_EN;
extern bit K2_cnt_EN;
extern bit K3_cnt_EN;
extern volatile bit longhit;


extern volatile bit key1_handle;
extern volatile bit key3_handle;
extern volatile uint16_t key1_duration;
extern volatile uint16_t key3_duration;
extern volatile uint16_t timer_1ms_count;


extern bit batlow, batlow1;


#define Power	 P26




//Motor
#define D_Phase  P05
#define C_Phase  P04
#define B_Phase  P13
#define A_Phase	 P14

#define	Motor_D	 P05
#define	Motor_C  P04
#define	Motor_B	 P13
#define	Motor_A	 P14

#define High 0
#define Low  1
#define Coil_A {A_Phase = Low; B_Phase = High; C_Phase = High; D_Phase = High;}
#define Coil_B {A_Phase = High; B_Phase = Low; C_Phase = High; D_Phase = High;}
#define Coil_C {A_Phase = High; B_Phase = High; C_Phase = Low; D_Phase = High;}
#define Coil_D {A_Phase = High; B_Phase = High; C_Phase = High; D_Phase = Low;}



#define Coil_OFF {A_Phase = High; B_Phase = High; C_Phase = High; D_Phase = High;}



#define Time_1ms_Offset		63536	//(65536 - 2000)
#define Time_10ms_Offset	45536	//(65536 - 20000)


extern bit Bit_1_ms_Buff,Bit_N_ms_Buff,Bit_Toggle;

extern uint16_t Battery_ADC_Wait_Time ;
extern uint16_t Delay_Time_Count;
extern bit Delay_Open ;
extern uint16_t Delay_Time ;
extern bit Delay_Over ;

extern bit Power_count_clean;
extern bit Center_Line_Control;


//UART Used
extern bit Get_String_Buff ;
extern uint8_t Get_String_Wait_Time ;
//UART Used END
extern int Num,Count_Toggle;

//Motor
#define Direction_Clockwise		0x0F
#define Direction_Cclockwise	0xF0

#define Motor_Fast_Speed		1		// 最快速度 (约3000步/秒)
#define Motor_Middle_Speed		3		// 中等速度 (约300步/秒)
#define Motor_Slow_Speed		17		// 慢速 (约60步/秒)
#define Motor_Slowest_Speed		20		// 最慢速 (约50步/秒)

#define Motor_Auto_Rotate_Speed		3		// 自转速度使用中等速度
#define Motor_Key_Control_Speed		17		// 按键控制使用慢速

extern uint8_t Motor_Direction_Data;
extern uint16_t Motor_Speed_Data ;
//Motor END


void Init_RAM_Variant(void) ;


#endif

